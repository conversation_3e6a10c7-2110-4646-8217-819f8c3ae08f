#!/usr/bin/env python3
"""
测试中文字体显示
"""

import matplotlib.pyplot as plt
import matplotlib.font_manager as fm
import os

# 确保输出目录存在
os.makedirs('output', exist_ok=True)

# 设置中文字体
def setup_chinese_font():
    """设置中文字体，支持不同操作系统"""
    import platform
    
    # 根据操作系统选择合适的中文字体
    system = platform.system()
    
    if system == "Darwin":  # macOS
        fonts = ['PingFang SC', 'Hiragino Sans GB', 'STHeiti', 'SimHei', 'Arial Unicode MS']
    elif system == "Windows":  # Windows
        fonts = ['Microsoft YaHei', 'SimHei', 'KaiTi', 'FangSong']
    else:  # Linux
        fonts = ['Noto Sans CJK SC', 'WenQuanYi Micro Hei', 'DejaVu Sans']
    
    # 获取系统可用字体
    available_fonts = [f.name for f in fm.fontManager.ttflist]
    
    # 尝试设置字体
    font_found = False
    for font in fonts:
        if font in available_fonts:
            plt.rcParams['font.sans-serif'] = [font]
            plt.rcParams['font.family'] = 'sans-serif'
            print(f"✅ 成功设置中文字体: {font}")
            font_found = True
            break
    
    if not font_found:
        # 如果没找到指定字体，尝试查找任何包含中文的字体
        chinese_fonts = []
        for font_name in available_fonts:
            if any(keyword in font_name.lower() for keyword in ['chinese', 'cjk', 'han', 'ping', 'hei', 'song']):
                chinese_fonts.append(font_name)
        
        if chinese_fonts:
            plt.rcParams['font.sans-serif'] = [chinese_fonts[0]]
            plt.rcParams['font.family'] = 'sans-serif'
            print(f"✅ 找到中文字体: {chinese_fonts[0]}")
        else:
            print("⚠️ 未找到合适的中文字体，可能显示为方框")
    
    # 解决负号显示问题
    plt.rcParams['axes.unicode_minus'] = False

# 设置字体
setup_chinese_font()

# 创建测试图表
def create_test_chart():
    fig, ax = plt.subplots(figsize=(10, 6))
    
    # 测试各种中文文本
    test_texts = [
        "c宝数字人分析师工作流程",
        "用户提问：上个季度非一线占比如何？",
        "Transformer理解问题",
        "数据检索与分析",
        "生成回答包含数据可视化和解释",
        "RAG知识空间增强",
        "提供专业知识、最新数据和行业背景"
    ]
    
    # 在不同位置显示文本
    for i, text in enumerate(test_texts):
        y_pos = 6 - i * 0.8
        ax.text(0.5, y_pos, text, fontsize=14, ha='left', va='center')
        ax.text(0.3, y_pos, f"{i+1}.", fontsize=14, ha='right', va='center', fontweight='bold')
    
    ax.set_xlim(0, 10)
    ax.set_ylim(0, 7)
    ax.set_title('中文字体显示测试', fontsize=18, fontweight='bold', pad=20)
    ax.axis('off')
    
    # 添加说明
    ax.text(5, 0.5, '如果你能看到清晰的中文字符，说明字体设置成功！', 
            ha='center', fontsize=12, style='italic', 
            bbox=dict(boxstyle="round,pad=0.3", facecolor="lightblue", alpha=0.7))
    
    plt.tight_layout()
    plt.savefig('output/chinese_font_test.png', dpi=300, bbox_inches='tight')
    plt.close()
    print("✅ 中文字体测试图表已生成: output/chinese_font_test.png")

if __name__ == "__main__":
    create_test_chart()
