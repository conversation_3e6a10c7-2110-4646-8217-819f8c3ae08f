import os
import yaml
from typing import Dict, Any, Optional
from pathlib import Path
import logging

logger = logging.getLogger(__name__)

class ConfigLoader:
    """配置加载器,实现单例模式,支持分环境配置"""
    
    _instance: Optional['ConfigLoader'] = None
    _config: Optional[Dict[str, Any]] = None

    def __new__(cls) -> 'ConfigLoader':
        if cls._instance is None:
            cls._instance = super(ConfigLoader, cls).__new__(cls)
        return cls._instance

    def __init__(self) -> None:
        if self._config is None:
            self._config = self._load_config()

    def _load_config(self) -> Dict[str, Any]:
        """
        加载配置文件
        
        Returns:
            Dict[str, Any]: 合并后的配置字典
        """
        project_root = Path(__file__).parent.parent.parent
        env = os.getenv('ENV', 'dev')
        config = {}
        
        # 加载基础配置
        base_config_path = project_root / 'config' / 'config.yaml'
        config = self._load_yaml_file(base_config_path, "基础配置")
        
        # 加载环境特定配置
        env_config_path = project_root / 'config' / f'config.{env}.yaml'
        if env_config_path.exists():
            env_config = self._load_yaml_file(env_config_path, f"{env}环境配置")
            if env_config:
                self._update_dict(config, env_config)
        
        return config

    def _load_yaml_file(self, file_path: Path, config_type: str) -> Dict[str, Any]:
        """
        加载YAML配置文件
        
        Args:
            file_path: 配置文件路径
            config_type: 配置类型描述
            
        Returns:
            Dict[str, Any]: 配置字典
        """
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                return yaml.safe_load(f) or {}
        except FileNotFoundError:
            logger.warning(f"未找到{config_type}文件: {file_path}")
        except Exception as e:
            logger.error(f"加载{config_type}文件失败: {file_path}, 错误: {e}")
        return {}
    
    def _update_dict(self, base_dict: Dict[str, Any], update_dict: Dict[str, Any]) -> None:
        """
        递归更新字典
        
        Args:
            base_dict: 基础字典
            update_dict: 用于更新的字典
        """
        for key, value in update_dict.items():
            if isinstance(value, dict) and isinstance(base_dict.get(key), dict):
                self._update_dict(base_dict[key], value)
            else:
                base_dict[key] = value

    def get(self, key: str, default: Any = None) -> Any:
        """
        获取配置值
        
        Args:
            key: 配置键,支持点号分隔的多级键
            default: 默认值
            
        Returns:
            Any: 配置值或默认值
        """
        if not self._config:
            return default
            
        value = self._config
        for k in key.split('.'):
            if not isinstance(value, dict):
                return default
            value = value.get(k)
            if value is None:
                return default
                
        return value

# 创建全局配置实例
config = ConfigLoader()
