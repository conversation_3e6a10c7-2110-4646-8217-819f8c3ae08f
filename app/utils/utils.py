import json ,os
from typing import Optional, Dict, List,Any


def load_user_data(user_id: str,user_data_dir:str) -> Dict[str, Any]:
    """加载用户数据文件"""
    user_file_path = os.path.join(user_data_dir, f"{user_id}.json")
    try:
        with open(user_file_path, 'r') as f:
            return json.load(f)
    except FileNotFoundError:
        return {"reports": []}

def save_user_data(user_id: str,user_data_dir:str, user_data: Dict[str, Any]) -> None:
    """保存用户数据到文件"""
    user_file_path = os.path.join(user_data_dir, f"{user_id}.json")
    with open(user_file_path, 'w') as f:
        json.dump(user_data, f, ensure_ascii=False, indent=2)