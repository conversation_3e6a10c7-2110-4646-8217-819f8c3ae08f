<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>经分月报生成系统</title>
    <link rel="stylesheet" href="{{ url_for('static', filename='css/dashboard.css') }}">
    <link href="https://cdn.bootcdn.net/ajax/libs/font-awesome/5.15.4/css/all.min.css" rel="stylesheet">
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Noto+Sans+SC:wght@400;500;700&display=swap" rel="stylesheet">
    <style>
        /* 添加中文字体支持 */
        * {
            font-family: 'SF Pro Display', 'SF Pro Text', 'Noto Sans SC', -apple-system, BlinkMacSystemFont, 'Helvetica Neue', 'Arial', sans-serif;
        }
    </style>
</head>
<body>
    <div class="container">
        <!-- 左侧对话区域 -->
        <div class="chat-container">
            <div class="chat-header">
                <h2><i class="fas fa-robot" style="margin-right: 8px;"></i>智能助手</h2>
            </div>
            <div class="chat-messages" id="chat-messages">
                <!-- 消息将在这里动态添加 -->
            </div>
            <div class="chat-input">
                <textarea id="user-input" placeholder="请输入您的问题或指令..."></textarea>
                <button id="send-btn"><i class="fas fa-paper-plane"></i></button>
            </div>
        </div>
        
        <!-- 右侧文档和日志区域 -->
        <div class="document-container">
            <div class="document-header">
                <h2><i class="fas fa-file-alt" style="margin-right: 8px;"></i>文档预览</h2>
            </div>
            <div class="document-content" id="document-content">
                <!-- 文档内容将在这里显示 -->
                <div class="placeholder">
                    <i class="fas fa-file-alt"></i>
                    <p>请在对话框中输入模板链接后查看模板月报</p>
                </div>
            </div>
            <div class="log-container">
                <div class="log-header">
                    <h3><i class="fas fa-terminal" style="margin-right: 6px;"></i>处理日志</h3>
                    <button id="clear-log-btn"><i class="fas fa-trash" style="margin-right: 4px;"></i>清空</button>
                </div>
                <div class="log-content" id="log-content">
                    <!-- 日志内容将在这里显示 -->
                </div>
            </div>
        </div>
    </div>

    <script src="{{ url_for('static', filename='js/dashboard.js') }}"></script>
</body>
</html>