import requests
from typing import Dict, Any
import contextlib

def upload2rag(file_path: str) -> requests.Response:
    """上传文件到RAG系统
    
    Args:
        file_path: 文件路径
        
    Returns:
        requests.Response: 上传响应
    """
    url = 'http://localhost:9621/documents/file'
    print(file_path)
    file_name = file_path.split('/')[-1]
    # 使用 contextlib 确保文件正确关闭
    with open(file_path, 'rb') as f:
        files = {
            'file': (file_name, f)
        }
        data = {
            'description': 'Optional description'
        }
        
        try:
            response = requests.post(
                url,
                files=files,
                data=data,
                timeout=30  # 添加超时设置
            )
            response.raise_for_status()  # 检查响应状态
            return response
        except requests.exceptions.RequestException as e:
            raise Exception(f"上传文件失败: {str(e)}")


def upload_batch2rag(file_paths: str) -> requests.Response:
    """上传文件到RAG系统
    
    Args:
        file_paths: 文件路径
        
    Returns:
        requests.Response: 上传响应
    """
    url = "http://0.0.0.0:9621/documents/file_batch"
    headers = {
        "accept": "application/json"
    }
    files = []
    for path in file_paths:
        files.append(
            ('files', (path.split('/')[-1], open(path, 'rb'), 'text/x-markdown'))
        )

    response = requests.post(url, headers=headers, files=files)

    print(response.status_code)
    print(response.text)
    return response


def query_rag(query: str,param = None) -> Dict[str, Any]:
    """查询RAG系统
    
    Args:
        query: 查询内容
        
    Returns:
        Dict: 查询响应
    """
    url = "http://0.0.0.0:9621/query"
    
    payload = {
        "query": query,
        "mode": "hybrid",
        "response_format": "multiple paragraphs",
        "top_k": 10,
        "max_token_for_text_unit": 4000,
        "max_token_for_global_context": 4000,
        "max_token_for_local_context": 4000,
        "history_turns": 1,
        "only_need_context":False,
        "only_need_prompt":False,
    
    }

    if param:
        for k,v in param.items():
            payload[k] = v

    
    headers = {"Content-Type": "application/json"}
    
    max_retry = 5
    try:
        while max_retry > 0:
                response = requests.post(
                    url,
                    json=payload,
                    headers=headers,
                    timeout=30  # 添加超时设置
                )
                response.raise_for_status()  # 检查响应状态
                # print(query)
                # print(response.json()['response'])
                if '无法回答' in response.json()['response'] or '无法准确回答' in response.json()['response'] \
                        or '无法给出' in response.json()['response'] or '无法根据现有知识回答该问题' in response.json()['response'] \
                        or '无法查询' in response.json()['response'] or '无法提供' in response.json()['response']:
                    max_retry -= 1
                    continue
                return response.json()['response']
        
        return response.json()['response']
      
    except requests.exceptions.RequestException as e:
        raise Exception(f"查询失败: {str(e)}")
    except KeyError:
        raise Exception("响应格式错误")


