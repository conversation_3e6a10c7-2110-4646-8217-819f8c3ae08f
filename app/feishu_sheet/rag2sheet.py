
from cgi import print_arguments
import requests
from pathlib import Path
import lark_oapi as lark
import yaml
from lark_oapi.api.sheets.v3 import *
from app.auth.auth import get_token
import pandas as pd 
from app.utils.config_loader import config

import lark_oapi as lark
import json
from requests_toolbelt import MultipartEncoder
from app.feishu_sheet.feishu_sheet import insert_sheet
from app.rag.rag import query_rag
from app.llm.llm import llm
from app.utils.utils import load_user_data, save_user_data


# 设置数据目录
base_dir = Path(__file__).parent.parent.parent
data_dir = base_dir / 'data'
user_data_dir = data_dir / 'users'
user_data_dir.mkdir(parents=True, exist_ok=True)




def duty_fit_rag2sheet(sheet_toekn,user_access_token):
    group2data={}

    key2line_number={
        '抖音群组':5,
        '直播群组':6,
        '头条群组':7,
        '垂直群组':8,
        '广告群组':10,
        '生服群组':11,
        '电商群组':12,
        '运支':14    
    }
    for group in ['抖音群组',"直播群组",'头条群组','垂直群组','广告群组','生服群组','电商群组','运支']:
        param = {'mode':"naive"}
        query = "从{0}的月报中查询 vs规划表格中， 量级变化、迁移节奏、招聘偏离、主动举措的总人力值分别为多少？".format(group)
        if group == '运支':
            query = "从{0}的月报中查询 vs规划表格中， 部门为运支，量级变化、迁移节奏、招聘偏离、主动举措的总人力值分别为多少？".format(group)

        param['hl_keywords'] = [group]
        result = query_rag(query,param=param)
        prompt ="从以下的文本回答中提取量级变化、迁移节奏、招聘偏离、主动举措的总人力值，如果文本中没有值，则默认为0，结果四舍五入到整数。以json格式返回，key 分别为量级变化,迁移节奏,招聘偏离,主动举措\n"
        prompt += result
        prompt += "\n"
    
        print(query)
        print(result)
        print('--'*20)
        llm_result = llm.chat(prompt)
        js  = json.loads(llm_result)
        group2data[group] = js

        line = key2line_number[group]
        res=insert_sheet(sheet_toekn,"F{0}".format(line),js['量级变化'],user_access_token)
        print('{0}插入量级变化数据,{1}'.format(group,res))
        insert_sheet(sheet_toekn,"G{0}".format(line),js['迁移节奏'],user_access_token)
        insert_sheet(sheet_toekn,"H{0}".format(line),js['招聘偏离'],user_access_token)
        insert_sheet(sheet_toekn,"I{0}".format(line),js['主动举措'],user_access_token)

        


def duty_fit_rag2sheet(user_id,sheet_toekn,user_access_token):
    group2data={}

    user_data = load_user_data(user_id,user_data_dir)

    group2file_path=user_data['group2file_path']
    key2line_number={
        '抖音群组':5,
        '直播群组':6,
        '头条群组':7,
        '垂直群组':8,
        '广告群组':10,
        '生服群组':11,
        '电商群组':12,
        '运营支持中心':14,
        "DMC":15    
    }
    for group in ['抖音群组',"直播群组",'头条群组','垂直群组','广告群组','生服群组','电商群组','运营支持中心',"DMC"]:
        
        prompt ="从以下的markdown文件中提取VS规划表格中量级变化、迁移节奏、招聘差异、主动举措的总人力值，如果文本中没有值，则默认为0，结果四舍五入到整数。注意在文件中改值的+-号。以json格式返回（能直接通过python进行解析），key 分别为量级变化,迁移节奏,招聘差异,主动举措。注意招聘偏离、主动举措在表格中的正负号，直接从表格中提取数据，不要去改变数据的正负。\n 文本内容如下：\n{{report}}"
        file_path = group2file_path[group]
        print(file_path)

        with open(file_path) as fr:
            report = fr.read()
        prompt = prompt.replace("{{report}}", report)
        # print(prompt)
        try:
            llm_result = llm.chat(prompt)
            print(llm_result)
            js  = json.loads(llm_result)

            print(json.loads(llm_result))
            group2data[group] = js
            line = key2line_number[group]
            res=insert_sheet(sheet_toekn,"F{0}".format(line),js['量级变化'],user_access_token)
            res=insert_sheet(sheet_toekn,"G{0}".format(line),js['迁移节奏'],user_access_token)
            res=insert_sheet(sheet_toekn,"H{0}".format(line),js['招聘差异'],user_access_token)
            res=insert_sheet(sheet_toekn,"I{0}".format(line),js['主动举措'],user_access_token)

            print('--'*20)

        except Exception as e:
            print("llm error",e)
            continue 
           

        


def task_cnt_rag2sheet(user_id,sheet_toekn,user_access_token):
    user_data = load_user_data(user_id,user_data_dir)
    group2file_path=user_data['group2file_path']

    group2data={}
    key2line_number={
        '抖音群组':6,
        '直播群组':7,
        '头条群组':8,
        '垂直群组':9,
        '广告群组':11,
        '生服群组':12,
        '电商群组':13,
        '运营支持中心':14    
    }
    for group in ['抖音群组',"直播群组",'头条群组','垂直群组','广告群组','生服群组','电商群组','运营支持中心']:
        file_path = group2file_path[group]
        with open(file_path) as fr:
            report = fr.read()

        prompt = "从以下的markdown文件中提取人审量级波动分析表格（或者X月整体量级波动情况表格、量级变化vs基期表格)中查询跟12月对比群组级别 新业务（新增业务）、主动降量、存量变动（被动降量+存量增量）的人审量级分别为多少，如果文本中没有值，则默认为0，结果四舍五入到整数。以json格式返回（能直接通过python进行解析），key 分别为新增业务、主动降量、存量变动。直接从表格中提取数据，不要去改变数据的正负。\n 文本内容如下：\n{{report}}"
        
        prompt = prompt.replace("{{report}}", report)


        try:
            llm_result = llm.chat(prompt)
            print(llm_result)
            js  = json.loads(llm_result)
            print(json.loads(llm_result))
            group2data[group] = js
            line = key2line_number[group]
            insert_sheet(sheet_toekn,"J{0}".format(line),js['新增业务'],user_access_token)
            insert_sheet(sheet_toekn,"L{0}".format(line),js['存量变动'],user_access_token)
            insert_sheet(sheet_toekn,"N{0}".format(line),js['主动降量'],user_access_token)  

            print('--'*20)

        except Exception as e:
            print("llm error",e)
            continue 


def person_change_rag2sheet(user_id,sheet_toekn,user_access_token):
    user_data = load_user_data(user_id,user_data_dir)
    group2file_path=user_data['group2file_path']
    group2data={}

    key2line_number={
            '抖音群组':5,
            '直播群组':6,
            '头条群组':7,
            '垂直群组':8,
            '广告群组':10,
            '生服群组':11,
            '电商群组':12,
            '运营支持中心':13    
        }


    for group in ['抖音群组',"直播群组",'头条群组','垂直群组','广告群组','生服群组','电商群组','运营支持中心']:

        file_path = group2file_path[group]
        with open(file_path) as fr:
            report = fr.read()

        prompt = "从以下的markdown文件中提取的管理动作收益表格中查询跟12月对比 群组的主动降量、综合人效提升、非一线优化的人数、12月双跑&冗余、被动量级变化、众包模式替代主审HC、新业务量级变化（TCS/广审）、新业务量级变化（非TCS /非广审）、迁移双跑人力、储备新人&冗余待调整 对应人力级分别为多少，如果文本中没有值，则默认为0，结果四舍五入到整数。以json格式返回（能直接通过python进行解析），key 分别为主动降量、综合人效提升、非一线优化、12月双跑&冗余、被动量级变化、众包模式替代主审HC、新业务量级变化_TCS_广审、新业务量级变化_非TCS_非广审、迁移双跑人力、储备新人&冗余待调整。直接从表格中提取数据，不要去改变数据的正负。\n 文本内容如下：\n{{report}}"
        
        prompt = prompt.replace("{{report}}", report)

        
        llm_result = llm.chat(prompt)
        print(llm_result)
        js  = json.loads(llm_result)
        print(json.loads(llm_result))
        group2data[group] = js
        line = key2line_number[group]
        try:
            insert_sheet(sheet_toekn,"G{0}".format(line),js['主动降量'],user_access_token)
            insert_sheet(sheet_toekn,"H{0}".format(line),js['综合人效提升'],user_access_token)
            insert_sheet(sheet_toekn,"I{0}".format(line),js['非一线优化'],user_access_token) 
            insert_sheet(sheet_toekn,"J{0}".format(line),js['12月双跑&冗余'],user_access_token)
            insert_sheet(sheet_toekn,"L{0}".format(line),js['被动量级变化'],user_access_token)
            insert_sheet(sheet_toekn,"M{0}".format(line),js['众包模式替代主审HC'],user_access_token) 
            insert_sheet(sheet_toekn,"N{0}".format(line),js['新业务量级变化_TCS_广审'],user_access_token)
            insert_sheet(sheet_toekn,"O{0}".format(line),js['新业务量级变化_非TCS_非广审'],user_access_token)    
            insert_sheet(sheet_toekn,"S{0}".format(line),js['迁移双跑人力'],user_access_token)
            insert_sheet(sheet_toekn,"T{0}".format(line),js['储备新人&冗余待调整'],user_access_token)
        except Exception as e:
            print("llm error",e)
            continue

        print('--'*20)

    

def index_rag2sheet(user_id,sheet_toekn,user_access_token):
    group2data={}
    user_data = load_user_data(user_id,user_data_dir)
    group2file_path=user_data['group2file_path']
    key2line_number={
            '抖音群组':4,
            '直播群组':5,
            '头条群组':6,
            '垂直群组':7,
            '广告群组':9,
            '生服群组':10,
            '电商群组':11,
            '运营支持中心':12    
        }

    for group in ['抖音群组',"直播群组",'头条群组','垂直群组','广告群组','生服群组','电商群组','运营支持中心']:
        file_path = group2file_path[group]
        with open(file_path) as fr:
            report = fr.read()

        prompt = "从以下的markdown文件中提取的北极星指标交付情况表格和文本中查询和总结群组的交付指标个数、达成个数、未出数/季度追踪、未达成个数 分别为多少，如果文本中没有值，则默认为0，结果四舍五入到整数。同时输出思考过程（包含哪些指标、指标完成情况），可以为参考的文本内容或者表格内容。以json格式返回（能直接通过python进行解析），key 分别为交付指标个数、达成个数、未出数/季度追踪、未达成个数、思考过程。 可以根据表格中是否达成进行判断。\n 文本内容如下：\n{{report}}"
        
        prompt = prompt.replace("{{report}}", report)

        
        llm_result = llm.chat(prompt)
        print(llm_result)
        js  = json.loads(llm_result)
        print(json.loads(llm_result))
        group2data[group] = js
        line = key2line_number[group]

        insert_sheet(sheet_toekn,"B{0}".format(line),js['交付指标个数'],user_access_token)
        insert_sheet(sheet_toekn,"C{0}".format(line),js['达成个数'],user_access_token)
        insert_sheet(sheet_toekn,"D{0}".format(line),js['未出数/季度追踪'],user_access_token)
        insert_sheet(sheet_toekn,"E{0}".format(line),js['未达成个数'],user_access_token)

   

   
    