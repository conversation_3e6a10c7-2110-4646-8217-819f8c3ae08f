import requests
from pathlib import Path
import lark_oapi as lark
import yaml
from lark_oapi.api.sheets.v3 import *
from app.auth.auth import get_token
import pandas as pd 
from app.utils.config_loader import config

import lark_oapi as lark
import json
from requests_toolbelt import MultipartEncoder


def read_feishu_sheet(token,app_access_token):

    sheet_file_token = token.strip().split('_')[0]
    sheet_token= token.strip().split('_')[1]
    # 构建请求URL和参数
    
    url = 'https://open.feishu.cn/open-apis/sheets/v2/spreadsheets/{0}/values/{1}'.format(sheet_file_token, sheet_token)

    print(url)
    params = {
        'valueRenderOption': 'ToString',
        'dateTimeRenderOption': 'FormattedString'
    }
    headers = {
        'Authorization': 'Bearer {0}'.format(app_access_token)
    }

    response = requests.get(url, params=params, headers=headers)
    return response.json()


def read_feishu_sheet_user_token(token,user_access_token):

    sheet_file_token = token.strip().split('_')[0]
    sheet_token= token.strip().split('_')[1]
    # 构建请求URL和参数
    
    url = 'https://open.feishu.cn/open-apis/sheets/v2/spreadsheets/{0}/values/{1}'.format(sheet_file_token, sheet_token)

    print(url)
    params = {
        'valueRenderOption': 'ToString',
        'dateTimeRenderOption': 'FormattedString'
    }
    headers = {
        'Authorization': 'Bearer {0}'.format(user_access_token)
    }

    response = requests.get(url, params=params, headers=headers)
    return response.json()


def group_report_status_check():
    app_token = get_token(config.get('feishu.app_id'), config.get('feishu.app_secret'))
    print(app_token)
    url = 'https://open.feishu.cn/open-apis/sheets/v2/spreadsheets/XwhYsS6Ishqt2ytqUTjcWlJgnog/values/ZbeDXh'
    params = {
        'valueRenderOption': 'FormattedValue',
        'dateTimeRenderOption': 'FormattedString'
    }
    headers = {
        'Authorization': 'Bearer {0}'.format(app_token)
    }

    response = requests.get(url, params=params, headers=headers)
    print(response.json())
    data = response.json()['data']['valueRange']['values']
    df = pd.DataFrame(data[1:], columns=data[0])
    print(df)

    df['月报链接'] = df.月报链接.apply(lambda x: x[0]['link'])

    return df


import json
from lark_oapi.api.docx.v1 import sheet
import requests

def insert_sheet(sheet_token,position,value,user_token):
    
    spreadsheet_token = sheet_token.strip().split('_')[0]
    sheet_id = sheet_token.strip().split('_')[1]
    # 目标 URL
    url = 'https://fsopen.bytedance.net/open-apis/sheets/v3/spreadsheets/{0}/sheets/{1}/values/batch_update?user_id_type=user_id'.format(spreadsheet_token,sheet_id)

    # 请求头
    headers = {
        'Content-Type': 'application/json',
        'Authorization': 'Bearer {0}'.format(user_token)
    }

    #请求体数据
    payload = {
        "value_ranges": [
            {
                "range": "{0}!{1}:{1}".format(sheet_id,position),
                "values": [
                    [
                        [
                            {
                                "text": {
                                    "text": value,
                                },
                                "type": "text"
                            }
                        ]
                    ]
                ]
            }
        ]
    }

    # 发送 POST 请求
    response = requests.post(url, headers=headers, json=payload)

    # 输出响应状态码和内容
    print(f"Status Code: {response.status_code}")
    print(f"Response Body: {response.text}")
    return response.text
    