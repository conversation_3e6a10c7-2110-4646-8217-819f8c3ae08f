from flask import render_template
from . import main_bp

@main_bp.route('/')
def index():
    return render_template('index.html', title='商业分析报告')

@main_bp.route('/about')
def about():
<<<<<<< HEAD
    return render_template('about.html', title='关于我们')
=======
    return render_template('about.html', title='关于我们')

@main_bp.route('/dashboard')
def dashboard():
    return render_template('dashboard.html', title='经分月报生成系统')

@main_bp.route('/feishu-preview')
@main_bp.route('/feishu_preview')
def feishu_preview():
    """飞书文档预览页面"""
    return render_template('feishu_preview.html')

>>>>>>> 517db714c9d0996c16d45bd2708a914a3ee6e74a
