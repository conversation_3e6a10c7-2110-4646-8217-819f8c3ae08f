from flask import request, jsonify
from . import api_bp
from app.services.document_service import get_document_info

@api_bp.route('/document-info', methods=['POST'])
def document_info():
    """
    接收文档URL，返回文档的基本信息
    请求体格式: {"url": "文档URL"}
    """
    data = request.get_json()
    
    if not data or 'url' not in data:
        return jsonify({
            'success': False,
            'error': '请提供文档URL'
        }), 400
    
    url = data['url']
    
    try:
        result = get_document_info(url)
        return jsonify({
            'success': True,
            'data': result
        })
    except ValueError as e:
        return jsonify({
            'success': False,
            'error': str(e)
        }), 400
    except Exception as e:
        return jsonify({
            'success': False,
            'error': f'处理文档失败: {str(e)}'
        }), 500