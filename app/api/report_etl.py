
from flask import request, jsonify, Blueprint, current_app as app
import requests
import json
report_etl_bp = Blueprint('report_etl', __name__)

def get_token(env):
    url = "https://ae-openapi.feishu.cn/auth/v1/appToken"
    if env=='4':
        # 开发
        payload = "{\"clientId\":\"c_f3e229f7d27f44a4a23c\",\"clientSecret\":\"b8aac3098ddf4a38a0669d9f3b1ec780\"}"
    else:
        # 线上
        payload = "{\"clientId\":\"c_e81509d57cb346a8b000\",\"clientSecret\":\"b9ba91912a5a48e58b93ed9fdc906cb7\"}"
    headers = {
    "Content-Type": "application/json"
    }
    response = requests.request("POST", url, headers=headers, data=payload.encode('utf-8'))
    token = json.loads(response.text)['data']['accessToken']
    return token
###获取文档表格sheet_id及block_id
@report_etl_bp.route('/get_sheet_block', methods=['POST'])
def get_sheet_block():
    data = request.get_json(force=True)
    document_id = data.get('document_id')
    url = "https://ae-openapi.feishu.cn/api/cloudfunction/v1/namespaces/package_5da4a7__c/invoke/get_bsa_blocks"
    payload = {"params":{"document_id":document_id}}
    headers = {
    "Authorization": get_token('1'),
    "Content-Type": "application/json"
    }

    response = requests.request("POST", url, headers=headers, json=payload)

    return response.json()
###月报数据批量写入
@report_etl_bp.route('/trigger_batch_insert', methods=['POST'])
def trigger_batch_insert():
    data = request.get_json(force=True)
    spreadsheet_token = data.get('spreadsheet_token')
    sheet_id = data.get('sheet_id')
    headers={
        'Content-Type': 'application/json',
        'Authorization': 'Bearer 8d7e482660b'
    }
    res = requests.post(url='https://apaas.feishu.cn/ae/api/v1/automation/namespaces/package_5da4a7__c/events/http/trigger_data_insert',headers=headers,json={'spreadsheet_token':spreadsheet_token,'sheet_id':sheet_id}) 
    return res.json()
###创建月报模版
@report_etl_bp.route('/create_doc_template', methods=['POST'])
def create_doc_template():
    data=request.get_json(force=True)
    open_id = data.get('open_id')
    url = "https://ae-openapi.feishu.cn/api/cloudfunction/v1/namespaces/package_5da4a7__c/invoke/weeklyreport_batchinsert_bysheettoken"
    payload = {"params":{"open_id":open_id}}
    headers = {
        "Authorization": get_token('1'),
        "Content-Type": "application/json"
    }

    response = requests.request("POST", url, headers=headers, json=payload)
    return response.json()
###获取数据解读id
def get_doc_briefId():
    docurl = 'https://bytedance.larkoffice.com/docx/E6Qzd7jQUoKusnxNBUXcjiBHnlc'
    url = "http://sophon-x.bytedance.net/sophon/workbench/brief/uid?docUrl="+ docurl +"&xagentId=OsAaqdnI"
    headers = {
        "token": 'hHFAqHPF3ZJcUg97a',
        "Content-Type": "application/json"
    }
    response = requests.request("GET", url, headers=headers)
    briefId = response.json()['data']['briefId']
    return briefId
###触发月报数据解读
@report_etl_bp.route('/trigger_data_analysis', methods=['POST'])
def trigger_data_analysis():
    data=request.get_json(force=True)
    document_id = data.get('document_id')
    briefId = get_doc_briefId()
    headers={
        'Content-Type': 'application/json',
        'token': 'hHFAqHPF3ZJcUg97a'
    }
    payload={
        "briefId": briefId,
        "docLink": 'https://bytedance.larkoffice.com/docx/'+document_id,
        "xagentId": "OsAaqdnI"
      }
    res = requests.post(url='https://sophon-x.bytedance.net/sophon/workbench/proxy/sophon-trigger/cqcBriefSend',headers=headers,json=payload)
    return res.json()