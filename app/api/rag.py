from app.feishu_doc import group_report
from flask import Blueprint, request, jsonify
import json
import os
import datetime
import requests
from typing import Dict, Any
from app.feishu_doc.group_report import group_report2md

from app.rag.rag import upload2rag,upload_batch2rag
from http import HTTPStatus

# 创建蓝图
rag_bp = Blueprint('rag', __name__)

@rag_bp.route('/rag_init', methods=['POST'])
def rag_init() -> tuple[Dict[str, Any], int]:
    """
    初始化RAG系统，处理用户文档并更新用户登录信息
    """
    try:
        # 获取并验证请求数据
        data = request.get_json(force=True)
        user_id = data.get('user_id')
        user_token = data.get('user_token')
        
        if not all([user_id, user_token]):
            return jsonify({
                "success": False,
                "message": "缺少必要参数"
            }), HTTPStatus.BAD_REQUEST
        
        # 初始化文件系统
        user_file_path = _initialize_file_system(user_id)
        
        # 清理现有RAG文档
        _clear_rag_documents()
        
        # 处理用户数据
        user_data = _process_user_data(user_id, user_token, user_file_path)
        
        # 更新用户信息
        _update_user_file(user_file_path, user_data)
        
        return jsonify({
            "success": True,
            "message": "RAG初始化成功",
            "user_id": user_id
        }), HTTPStatus.OK
        
    except Exception as e:
        return jsonify({
            "success": False,
            "message": f"初始化失败: {str(e)}"
        }), HTTPStatus.INTERNAL_SERVER_ERROR

def _initialize_file_system(user_id: str) -> str:
    """初始化文件系统并返回用户文件路径"""
    base_dir = os.path.dirname(os.path.dirname(os.path.dirname(__file__)))
    data_dir = os.path.join(base_dir, 'data')
    user_data_dir = os.path.join(data_dir, 'users')
    os.makedirs(user_data_dir, exist_ok=True)
    return os.path.join(user_data_dir, f"{user_id}.json")

def _clear_rag_documents() -> None:
    """清理RAG系统中的现有文档"""
    try:
        response = requests.delete("http://localhost:9621/documents")
        response.raise_for_status()
    except requests.RequestException as e:
        raise Exception(f"清理RAG文档失败: {str(e)}")

def _process_user_data(user_id: str, user_token: str, user_file_path: str) -> Dict[str, Any]:
    """处理用户数据并上传到RAG系统"""
    try:
        # 读取用户数据
        user_data = {}
        if os.path.exists(user_file_path):
            with open(user_file_path, 'r', encoding='utf-8') as f:
                user_data = json.load(f)
        
        # 处理群组报告信息
        group_report_info = user_data.get('group_report_info', {})

        file_paths = []
        for report in group_report_info:
            if report_url := report.get('月报链接'):
                file_path = group_report2md(user_token, report_url)
                file_paths.append(file_path)
            
        try:
            upload_batch2rag(file_paths=file_paths)
        except Exception as e:
            print(e)
            raise Exception(f"上传文件到RAG系统失败: {str(e)}")
                # finally:
                #     if os.path.exists(file_path):
                #         os.remove(file_path)
        # 更新登录时间
        user_data["last_login"] = datetime.datetime.now().isoformat()
        return user_data
        
    except Exception as e:
        print(e)
        raise Exception(f"处理用户数据失败: {str(e)}")

def _update_user_file(file_path: str, user_data: Dict[str, Any]) -> None:
    """更新用户文件"""
    try:
        with open(file_path, 'w', encoding='utf-8') as f:
            json.dump(user_data, f, ensure_ascii=False, indent=4)
    except Exception as e:
        raise Exception(f"更新用户文件失败: {str(e)}")
