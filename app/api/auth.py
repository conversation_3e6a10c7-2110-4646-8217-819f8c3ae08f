# from flask import Flask, render_template, request, jsonify
from flask import Blueprint, request, jsonify, current_app
from flask_cors import CORS # Add this import
import requests
import secrets
import string
import datetime
import hashlib
import time
import os
import json

from urllib3 import response # Import os for environment variables later if needed

# app = Flask(__name__)
auth_bp = Blueprint('auth', __name__)
CORS(auth_bp) # Initialize CORS

# --- Feishu JSSDK Signature Generation Logic ---
def secure_random_str(length=16):
    chars = string.ascii_letters + string.digits
    return ''.join(secrets.choice(chars) for _ in range(length))

def get_jsapi_ticket(access_token):
    url = "https://open.feishu.cn/open-apis/jssdk/ticket/get"
    headers = {
        "Authorization": f"Bearer {access_token}"
    }
    try:
        res = requests.post(url=url, headers=headers)
        res.raise_for_status() # Raise an exception for bad status codes
        data = res.json()
        if data.get('code') == 0:
            return data['data']['ticket']
        else:
            print(f"Error getting ticket: {data.get('msg')}")
            return None
    except requests.exceptions.RequestException as e:
        print(f"Request failed: {e}")
        return None

def generate_signature(ticket, nonce_str, timestamp, url):
    print('signature_timestamp: ', timestamp)
    str1 = f'jsapi_ticket={ticket}&noncestr={nonce_str}&timestamp={timestamp}&url={url}'
    print('str1: ', str1)
    # data = str1.encode('utf-8')
    # sha1_hash = hashlib.sha1(data).hexdigest()
    hash_object = hashlib.sha1(str1.encode())
    sha1_hash = hash_object.hexdigest()
    print("SHA-1 Hash:", sha1_hash)
    return sha1_hash

# --- Helper Function for JSSDK Context ---
def get_jssdk_context(access_token, app_id, open_id, current_url):
    """Generates the JSSDK context dictionary."""
    # ticket = get_jsapi_ticket(access_token)
    signature = None
    nonce_str = None
    timestamp = None

    if access_token:
        ticket = get_jsapi_ticket(access_token)
        nonce_str = secure_random_str()
        beijing_tz = datetime.timezone(datetime.timedelta(hours=8))
        beijing_now = datetime.datetime.now(beijing_tz)
        timestamp = int(beijing_now.timestamp() * 1000)
        signature = generate_signature(ticket, nonce_str, timestamp, current_url)
    elif access_token is None:
        nonce_str = secure_random_str()
        beijing_tz = datetime.timezone(datetime.timedelta(hours=8))
        beijing_now = datetime.datetime.now(beijing_tz)
        timestamp = int(beijing_now.timestamp() * 1000)
        signature = None
    else:
        print("Failed to get JSSDK ticket. Signature cannot be generated.")

    context = {
        'openId': open_id,
        'signature': signature,
        'appId': app_id,
        'timestamp': timestamp,
        'nonceStr': nonce_str,
        'url': current_url,
        'jsApiList': ['DocsComponent'] # Keep this consistent
    }
    return context

# --- Helper Function for Tenant Access Token ---
def get_token():
    url="https://open.larkoffice.com/open-apis/auth/v3/app_access_token/internal"


    headers = {
        "Content-Type":"application/json; charset=utf-8"
    }


    body = {"app_id": "cli_a66eb807d470900e",#"cli_9d7ae4db9fbbd102"#匹诺曹,
    "app_secret": "YCJNhQyIqRnOhlKgDzJJ4uJvoJQqvf71"#"kkAh7HsG2EVIc0kkhLKoBhCBFa03VcMv"
    }


    r = requests.post(url, data=json.dumps(body), headers=headers)


    # tenant_access_token = json.loads(r.text)['tenant_access_token']
    # print(tenant_access_token)
    # return tenant_access_token
    print(r.json())
    return r.json()

# --- Helper Function for User Access Token ---
def get_user_access_token(code):
    """Exchanges authorization code for user access token."""
    app_token_info = get_token()
    if 'app_access_token' not in app_token_info:
        print("Error getting app_access_token")
        return None

    app_access_token = app_token_info['app_access_token']

    url = "https://open.larkoffice.com/open-apis/authen/v1/oidc/access_token"
    headers = {
        "Authorization": f"Bearer {app_access_token}",
        "Content-Type": "application/json; charset=utf-8"
    }
    data = {
        "grant_type": "authorization_code",
        "code": code
    }
    try:
        res = requests.post(url=url, headers=headers, data=json.dumps(data))
        res.raise_for_status()
        response_data = res.json()
        if response_data.get('code') == 0:
            return response_data['data']['access_token']
        else:
            print(f"Error getting user access token: {response_data.get('msg')}")
            return None
    except requests.exceptions.RequestException as e:
        print(f"Request failed: {e}")
        return None
    except json.JSONDecodeError:
        print(f"Failed to decode JSON response: {res.text}")
        return None

# --- Helper Function for User Info (including OpenID) ---
def get_openid_from_token(user_access_token):
    """Fetches user info, including open_id, using the user access token."""
    url = "https://open.larkoffice.com/open-apis/authen/v1/user_info"
    headers = {
        "Authorization": f"Bearer {user_access_token}"
    }
    try:
        res = requests.get(url=url, headers=headers)
        res.raise_for_status()
        response_data = res.json()
        if response_data.get('code') == 0:
            # The open_id is within the 'data' dictionary
            return response_data.get('data', {}).get('open_id')
        else:
            print(f"Error getting user info: {response_data.get('msg')}")
            return None
    except requests.exceptions.RequestException as e:
        print(f"Request failed: {e}")
        return None
    except json.JSONDecodeError:
        print(f"Failed to decode JSON response: {res.text}")
        return None


# --- Flask Routes ---
@auth_bp.route('/get_open_id', methods=['POST'])
def api_get_open_id():
    try:
        data = request.get_json()
        access_token = data.get('access_token')
        if access_token:
            open_id = get_openid_from_token(access_token)
            if open_id:
                return jsonify({'open_id': open_id})
            else:
                return jsonify({'error': 'Failed to get open_id'}), 400
        else:
            return jsonify({'error': 'Access token not found in POST request body'}), 400
    except Exception as e:
        return jsonify({'error': str(e)}), 500

@auth_bp.route('/access_token', methods=['POST'])
def api_access_token():
    try:
        data = request.get_json()
        code = data.get('code')
        if code:
            access_token = get_user_access_token(code)
            if access_token:
                return jsonify({'access_token': access_token})
            else:
                return jsonify({'error': 'Failed to get user access token'}), 400
        else:
            return jsonify({'error': 'Code not found in POST request body'}), 400
    except Exception as e:
        return jsonify({'error': str(e)}), 500

@auth_bp.route('/jssdk_context', methods=['GET', 'POST'])
def api_jssdk_context():
    access_token = None
    if request.method == 'POST':
        
        data = request.get_json()
        access_token = data.get('access_token')
         

    # Fallback to hardcoded token if POST fails or method is GET
    if not access_token:
        print("Falling back to hardcoded access token.")
        # IMPORTANT: Hardcoding tokens is insecure. Use environment variables or a config management system.
        access_token = 'u-imCEtBfI91BaEhD1CWJfVh10hKjB502PO0G0h5Yw0J4u' # Replace with your actual token retrieval method

    app_id = 'cli_a66eb807d470900e' # Your App ID
    # Initialize open_id, potentially update it later
    open_id = ''#'ou_7e37b73cda3c4b2ef568e85d015eb06b' # Default or fallback Open ID

    # If we got a user access token, try to get the open_id from it
    if access_token:
        fetched_open_id = get_openid_from_token(access_token)
        if fetched_open_id:
            open_id = fetched_open_id # Update open_id if fetch is successful
        else:
            print("Failed to get open_id from user access token. Using default.")

    # Get URL from request or hardcode for testing
    # url = request.args.get('url', request.url_root) # Example: get url from query param or default
    current_url = request.args.get('url', 'http://127.0.0.1:5173/') # Allow overriding url via query param

    # if not access_token:
    #      return jsonify({'error': 'Failed to obtain access token'}), 400

    context = get_jssdk_context(access_token, app_id, open_id, current_url)
    # if not context.get('signature'): # Check if signature generation failed
    #     return jsonify({'error': 'Failed to generate JSSDK signature'}), 500

    context['access_token'] = access_token
    context['open_id'] = open_id
    response = jsonify(context)
    # response.headers.add('Access-Control-Allow-Origin', 'http://localhost:8000/') # Remove this line
    return response

# @auth_bp.route('/jssdk_context', methods=['GET', 'POST'])
# def api_jssdk_context():
#     access_token = None
#     if request.method == 'POST':
#         try:
#             data = request.get_json()
#             code = data.get('code')
#             if code:
#                 access_token = get_user_access_token(code)
#                 if not access_token:
#                     print("Failed to get user access token from code.")
#                 # else:
#                 #     # Fetch open_id using the obtained user access token
#                 #     fetched_open_id = get_openid_from_token(access_token)
#                 #     if fetched_open_id:
#                 #         open_id = fetched_open_id # Update open_id if fetch is successful
#                 #     else:
#                 #         print("Failed to get open_id from user access token.")
#             else:
#                 print("Code not found in POST request body.")
#         except Exception as e:
#             print(f"Error processing POST request: {e}")

#     # Fallback to hardcoded token if POST fails or method is GET
#     if not access_token:
#         print("Falling back to hardcoded access token.")
#         # IMPORTANT: Hardcoding tokens is insecure. Use environment variables or a config management system.
#         # access_token = 'u-i9j3I669l4NH4SZwGQPb4T51ndF050ErVa20k0y801hK' # Replace with your actual token retrieval method

#     app_id = 'cli_a66eb807d470900e' # Your App ID
#     # Initialize open_id, potentially update it later
#     open_id = 'ou_7e37b73cda3c4b2ef568e85d015eb06b' # Default or fallback Open ID

#     # If we got a user access token, try to get the open_id from it
#     if access_token:
#         fetched_open_id = get_openid_from_token(access_token)
#         if fetched_open_id:
#             open_id = fetched_open_id # Update open_id if fetch is successful
#         else:
#             print("Failed to get open_id from user access token. Using default.")

#     # Get URL from request or hardcode for testing
#     # url = request.args.get('url', request.url_root) # Example: get url from query param or default
#     current_url = request.args.get('url', 'http://127.0.0.1:5173/') # Allow overriding url via query param

#     # if not access_token:
#     #      return jsonify({'error': 'Failed to obtain access token'}), 400

#     context = get_jssdk_context(access_token, app_id, open_id, current_url)
#     # if not context.get('signature'): # Check if signature generation failed
#     #     return jsonify({'error': 'Failed to generate JSSDK signature'}), 500

#     context['access_token'] = access_token
#     context['open_id'] = open_id
#     response = jsonify(context)
#     # response.headers.add('Access-Control-Allow-Origin', 'http://localhost:8000/') # Remove this line
#     return response

# @app.route('/auth')
# def auth():
#     return render_template('auth.html')

# if __name__ == '__main__':
#     # Use 0.0.0.0 to make it accessible from network if needed, otherwise 127.0.0.1 is fine
#     app.run(host='127.0.0.1', port=5173)#, debug=True)
