from flask import Blueprint, request, jsonify
import json
import os
import datetime

# 创建蓝图
registration_bp = Blueprint('registration', __name__)

# 注意这里的路由路径，应该是 '/register' 而不是 '/registration'
@registration_bp.route('/register', methods=['POST'])
def register_user():
    """注册用户接口"""
    # 获取请求数据
    data = request.get_json(force=True)
    user_id = data.get('user_id')
    user_token = data.get('user_token')
    
    # 参数验证
    if not user_id:
        return jsonify({"success": False, "message": "缺少用户ID参数"}), 400
    if not user_token:
        return jsonify({"success": False, "message": "缺少用户令牌参数"}), 400
    
    # 创建用户数据目录
    base_dir = os.path.dirname(os.path.dirname(os.path.dirname(__file__)))
    data_dir = os.path.join(base_dir, 'data')
    user_data_dir = os.path.join(data_dir, 'users')
    os.makedirs(user_data_dir, exist_ok=True)
    
    # 用户信息文件路径
    user_file_path = os.path.join(user_data_dir, f"{user_id}.json")
    
    # 准备用户数据
    user_data = {
        "user_id": user_id,
        "user_token": user_token,
        "registration_time": datetime.datetime.now().isoformat(),
        "last_login": datetime.datetime.now().isoformat()
    }
    
    # 写入JSON文件
    with open(user_file_path, 'w', encoding='utf-8') as f:
        json.dump(user_data, f, ensure_ascii=False, indent=4)
    
    return jsonify({
        "success": True,
        "message": "用户注册成功",
        "user_id": user_id
    })


