from flask import Blueprint, request, jsonify, current_app
import os
from datetime import datetime
import pandas as pd
from typing import Dict, Optional
from app.feishu_doc.feishu_doc import (
    get_docid_from_url, 
    get_feishu_doc_blocks_user_token,
    parse_doc_file
)
from app.feishu_sheet.feishu_sheet import (
    group_report_status_check,
    read_feishu_sheet_user_token
)
from app.feishu_doc.group_report import save_group_report
from app.utils.utils import load_user_data, save_user_data
from app.feishu_doc.content_process import DocxTextModify

feishu_bp = Blueprint('feishu', __name__)

# 创建用户数据目录
base_dir = os.path.dirname(os.path.dirname(os.path.dirname(__file__)))
data_dir = os.path.join(base_dir, 'data')
user_data_dir = os.path.join(data_dir, 'users')
os.makedirs(user_data_dir, exist_ok=True)

def validate_request_data(data: Dict, required_fields: list) -> Optional[tuple]:
    """验证请求数据中的必要字段"""
    missing_fields = [field for field in required_fields if not data.get(field)]
    if missing_fields:
        return jsonify({"error": f"缺少必要参数: {', '.join(missing_fields)}"}), 400
    return None

@feishu_bp.route('/get_sheet_block', methods=['POST'])
def get_sheet_block():
    """获取飞书文档内容并处理sheet块"""
    try:
        data = request.get_json(force=True)
        validation_result = validate_request_data(data, ['url'])
        if validation_result:
            return validation_result

        url = data['url']
        user_id = data.get('user_id')
        user_token = data.get('user_token')

        user_data = load_user_data(user_id, user_data_dir)
        
        if not user_token:
            user_token = user_data.get('user_token')
            if not user_token:
                return jsonify({"error": "缺少用户令牌参数"}), 400

        doc_id = get_docid_from_url(url)
        doc_content = get_feishu_doc_blocks_user_token(doc_id, user_token=user_token)
        block_parse = parse_doc_file(doc_content)

        # 更新用户数据
        if 'doc_id' not in user_data:
            user_data.update({
                'doc_id': doc_id,
                'tmplate_url': url,
                'doc_content': doc_content,
                'last_login': datetime.now().isoformat()
            })
        else:
            user_data['last_login'] = datetime.now().isoformat()

        # 处理sheet块
        next_block_id = None
        next_sheet_token = None
        check_sheet_blocks = user_data.get('check_sheet_blocks', [])
        
        for idx, block_info in enumerate(block_parse):
            if block_info.block_id not in check_sheet_blocks:
                check_sheet_blocks.append(block_info.block_id)
                next_block_id = block_info.block_id
                next_sheet_token = block_info.token
                user_data['check_sheet_blocks'] = check_sheet_blocks
                break
        print('...get_sheet_block()...',block_info.block_id,idx,check_sheet_blocks)
        save_user_data(user_id, user_data_dir, user_data)

        return jsonify({
            "block_id": next_block_id,
            'message': f"开始第{idx+1}个表格数据写入....",
            "user_id": user_id,
            "doc_id": doc_id,
            'sheet_token': next_sheet_token
        })

    except Exception as e:
        current_app.logger.error(f"读取飞书文档失败: {str(e)}")
        return jsonify({"error": f"读取飞书文档失败: {str(e)}"}), 500

@feishu_bp.route('/get_doc_blocks_app_token', methods=['POST'])
def get_doc_blocks_app_token():
    """处理文档块应用令牌"""
    try:
        data = request.get_json(force=True)
        validation_result = validate_request_data(data, ['url', 'user_id', 'report_source'])
        if validation_result:
            return validation_result

        user_id = data['user_id']
        user_data = load_user_data(user_id)
        
        report_info = {
            "url": data['url'],
            "source": data['report_source'],
            "created_at": datetime.now().isoformat()
        }
        user_data.setdefault("reports", []).append(report_info)
        save_user_data(user_id, user_data)

        return jsonify({"success": True})

    except Exception as e:
        current_app.logger.error(f"上传报告失败: {str(e)}")
        return jsonify({"error": f"上传报告失败: {str(e)}"}), 500

@feishu_bp.route('/group_report_check', methods=['POST'])
def group_report_check():
    """检查群组报告状态"""
    try:
        data = request.get_json(force=True)
        validation_result = validate_request_data(data, ['user_id'])
        if validation_result:
            return validation_result

        user_id = data['user_id']
        user_data = load_user_data(user_id, user_data_dir)
        
        group_report_df = group_report_status_check()
        group_report_info = group_report_df.to_dict(orient='records')
        
        missing_reports = group_report_df.loc[group_report_df['月报链接'].isnull(), '群组'].tolist()
        
        user_data["group_report_info"] = group_report_info
        save_user_data(user_id, user_data_dir, user_data)

        response = {
            "group_report_status": not bool(missing_reports),
            "user_id": user_id,
            "message": f"请先完成以下群组月报配置: {','.join(missing_reports)}" if missing_reports else "完成群组月报配置检查"
        }
        
        return jsonify(response)

    except Exception as e:
        current_app.logger.error(f"群组月报配置失败: {str(e)}")
        return jsonify({
            "message": f"群组月报配置失败: {str(e)}",
            "group_report_status": False,
            "user_id": data.get('user_id')
        }), 500

@feishu_bp.route('/group_report_file', methods=['POST'])
def group_report_file():
    """处理群组报告文件"""
    try:
        data = request.get_json(force=True)
        validation_result = validate_request_data(data, ['user_id', 'user_token'])
        if validation_result:
            return validation_result

        user_id = data['user_id']
        user_token = data['user_token']
        
        user_data = load_user_data(user_id, user_data_dir)
        group_report_info = user_data.get('group_report_info', [])
        
        if not group_report_info:
            return jsonify({
                "error": "未找到群组报告信息",
                "user_id": user_id
            }), 404

        group2file_path = {}
        for report in group_report_info:
            if report_url := report.get('月报链接'):
                try:
                    file_path, group_name = save_group_report(user_token, report_url)
                    group2file_path[group_name] = file_path
                except Exception as e:
                    current_app.logger.error(f"处理报告失败: {str(e)}")
                    continue

        user_data['group2file_path'] = group2file_path
        save_user_data(user_id, user_data_dir, user_data)

        return jsonify({
            "message": "群组月报保存成功",
            "group2file_path": group2file_path,
            "user_id": user_id
        })

    except Exception as e:
        current_app.logger.error(f"保存群组月报失败: {str(e)}")
        return jsonify({
            "error": f"保存群组月报失败: {str(e)}",
            "user_id": user_id
        }), 500

@feishu_bp.route('/sheet_check', methods=['POST'])
def sheet_check():
    """检查表格填写状态"""
    try:
        data = request.get_json(force=True)
        validation_result = validate_request_data(data, ['user_id', 'user_token', 'sheet_token'])
        if validation_result:
            return validation_result

        user_id = data['user_id']
        user_token = data['user_token']
        sheet_token = data['sheet_token']

        response = read_feishu_sheet_user_token(sheet_token, user_token)
        values = response.get('data', {}).get('valueRange', {}).get('values')
        
        if not values:
            return jsonify({
                "sheet_done": True,
                "user_id": user_id,
                "sheet_token": sheet_token,
                "message": "表格数据为空"
            })

        df = pd.DataFrame(values)
        if df.empty:
            return jsonify({
                "sheet_done": True,
                "user_id": user_id,
                "sheet_token": sheet_token,
                "message": "表格无数据"
            })

        filtered_df = df[df[df.columns[0]] == '运营支持中心']
        # sheet_done = not (filtered_df == '#VALUE!').any().any()

        sheet_done = not (
            (filtered_df.isnull()) | (filtered_df == '') | (filtered_df == '#VALUE!')
        ).any().any()


        print(filtered_df)
        current_app.logger.info(f"表格检查完成: {sheet_token}，{sheet_done}")

        return jsonify({
            "sheet_done": sheet_done,
            "user_id": user_id,
            "sheet_token": sheet_token,
            "message": "表格填写完成" if sheet_done else "表格填写未完成"
        })

    except Exception as e:
        current_app.logger.error(f"表格检查失败: {str(e)}")
        return jsonify({
            "message": f"表格检查失败: {str(e)}",
            "sheet_done": True,
            "user_id": user_id,
            "sheet_token": sheet_token
        }), 500

@feishu_bp.route('/doc_text_modify', methods=['POST'])
def doc_text_modify():
    """修改文档文本内容"""
    try:
        data = request.get_json(force=True)
        validation_result = validate_request_data(data, ['user_token', 'doc_id', 'month', 'user_id'])
        if validation_result:
            return validation_result

        obj = DocxTextModify(
            data['user_token'],
            data['doc_id'],
            data['month'],
            data['user_id']
        )
        res = obj.modify_content()
        
        save_user_data(f"{data['user_id']}#block_id", user_data_dir, {"block_id": ""})
        return jsonify({"success": True, "message": res})

    except Exception as e:
        return jsonify({"error": f"文档改写失败: {str(e)}"}), 500

@feishu_bp.route('/get_modify_block_id', methods=['POST'])
def get_modify_block_id():
    """获取修改块ID"""
    try:
        data = request.get_json(force=True)
        validation_result = validate_request_data(data, ['user_id'])
        if validation_result:
            return validation_result

        block_id_data = load_user_data(f"{data['user_id']}#block_id", user_data_dir)
        if not block_id_data:
            return jsonify({"error": "未获取到数据"}), 400

        block_id = block_id_data.get('block_id')
        if block_id or block_id == "":
            return jsonify({"success": True, "block_id": block_id})

        return jsonify({"error": "未获取到数据"}), 400

    except Exception as e:
        current_app.logger.error(f"获取修改块ID失败: {str(e)}")
        return jsonify({"error": f"获取修改块ID失败: {str(e)}"}), 500
