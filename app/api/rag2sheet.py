

from flask import Blueprint, request, jsonify
import json
import os
import datetime
from app.feishu_sheet.rag2sheet import duty_fit_rag2sheet, task_cnt_rag2sheet,person_change_rag2sheet,index_rag2sheet
from app.utils.utils import load_user_data, save_user_data
from app.feishu_doc.feishu_doc import parse_doc_file
# 创建蓝图
rag2sheet_bp = Blueprint('rag2sheet_bp', __name__)

# 创建用户数据目录
base_dir = os.path.dirname(os.path.dirname(os.path.dirname(__file__)))
data_dir = os.path.join(base_dir, 'data')
user_data_dir = os.path.join(data_dir, 'users')
@rag2sheet_bp.route('/rag2sheet', methods=['POST'])
def rag2sheet():

    # 获取请求数据
    data = request.get_json(force=True)
    user_id = data.get('user_id')
    user_token = data.get('user_token')
    sheet_token = data.get('sheet_token')


    user_data = load_user_data(user_id,user_data_dir)
    doc_content = user_data['doc_content']
    block_parse = parse_doc_file(doc_content)
    

    sheet_index = -1
    for index,info in enumerate(block_parse):
        if info.token == sheet_token:
            sheet_index = index
            break
    if sheet_index == -1:
        return jsonify({"success": False, "message": "sheet_token不存在"}), 400

    if sheet_index == 0:
        duty_fit_rag2sheet(user_id,sheet_toekn=sheet_token,user_access_token=user_token)
        return jsonify({"success": True, "message": "表格写入成功"})

    if sheet_index == 2:
        task_cnt_rag2sheet(user_id,sheet_toekn=sheet_token,user_access_token=user_token)
        return jsonify({"success": True, "message": "表格写入成功"})

    if sheet_index == 4:
        person_change_rag2sheet(user_id,sheet_toekn=sheet_token,user_access_token=user_token)
        return jsonify({"success": True, "message": "表格写入成功"})
    
    if sheet_index == 5:
        index_rag2sheet(user_id,sheet_token,user_token)
        return jsonify({"success": True, "message": "表格写入成功"})

    return jsonify({"success": True, "message": "无需写入"})