import requests
from bs4 import BeautifulSoup
import urllib.parse

def get_document_info(url):
    """
    获取文档信息
    
    Args:
        url: 文档URL
        
    Returns:
        dict: 文档信息
        
    Raises:
        ValueError: URL格式不正确
        requests.exceptions.RequestException: 请求失败
    """
    # 验证URL格式
    parsed_url = urllib.parse.urlparse(url)
    if not parsed_url.scheme or not parsed_url.netloc:
        raise ValueError('URL格式不正确')
        
    # 获取文档内容
    response = requests.get(url, timeout=10)
    response.raise_for_status()  # 如果请求失败则抛出异常
    
    # 解析HTML内容
    soup = BeautifulSoup(response.text, 'html.parser')
    
    # 提取文档信息
    title = soup.title.string if soup.title else '未知标题'
    
    # 提取元数据
    meta_data = {}
    for meta in soup.find_all('meta'):
        if meta.get('name') and meta.get('content'):
            meta_data[meta.get('name')] = meta.get('content')
    
    # 提取正文内容摘要
    paragraphs = soup.find_all('p')
    content_preview = ' '.join([p.text for p in paragraphs[:3]]) if paragraphs else ''
    if len(content_preview) > 300:
        content_preview = content_preview[:300] + '...'
    
    return {
        'url': url,
        'title': title,
        'meta_data': meta_data,
        'content_preview': content_preview,
        'content_length': len(response.text),
        'content_type': response.headers.get('Content-Type', '未知类型')
    }