import copy,re,requests
import pandas as pd # new
from pathlib import Path
from app.utils.utils import load_user_data, save_user_data
from app.llm.llm import llm
import os

# 设置数据目录
base_dir = Path(__file__).parent.parent.parent
data_dir = base_dir / 'data'
user_data_dir = data_dir / 'users'
user_data_dir.mkdir(parents=True, exist_ok=True)



class DocxTextModify:
    def __init__(self, user_token, doc_token,month, user_id):
        self.user_token = user_token
        self.user_id = user_id
        self.doc_token = doc_token
        self.user_id = user_id
        self.url = "https://fsopen.bytedance.net/open-apis"
        doc_data = self.get_blocks_and_sheets(user_token, doc_token)
        self.sheet_ids = doc_data[0]
        self.blocks = doc_data[1]
        self.month = month
        base_dir = os.path.dirname(os.path.dirname(os.path.dirname(__file__)))
        data_dir = os.path.join(base_dir, 'data')
        self.user_data_dir = os.path.join(data_dir, 'users')
        os.makedirs(self.user_data_dir, exist_ok=True)

        
    # 获取文档中所有的block和表格
    def get_blocks_and_sheets(self, user_access_token, doc_token):
        url = f"{self.url}/docx/v1/documents/{self.doc_token}/blocks"
        
        headers = {
            "Authorization": "Bearer " + self.user_token,
        }
        # print(headers)
        params = {
            "document_revision_id": "-1",
            "page_size": 500
        }
        response = requests.get(url, headers=headers, params=params)
        print(response.text)
        res = response.json()
        sheets_dict = {}
        sheets_cnt = 1
        for block in res['data']['items']:
            if block['block_type'] == 30:
                sheets_dict['表格%s'%(sheets_cnt)] = block['sheet']['token']
                sheets_cnt += 1
        return sheets_dict, res['data']['items']
    
    # new
    def get_sheets_datas(self, spreadsheetToken,range_str):
        url = f"{self.url}/sheets/v2/spreadsheets/{spreadsheetToken}/values/{range_str}"
        headers = {
            "Authorization": "Bearer " + self.user_token,
            "Content-Type": "application/json; charset=utf-8"
        }
        params = {
            "valueRenderOption": "FormattedValue",
        }
        response = requests.get(url, headers=headers, params=params)
        # print(response.text)
        res = response.json()
        print(res)
        return res['data']['valueRange']['values']
    # new
    def get_sheet_data_from_kv(self, key, value):
        # key,value = list(jsond['index'].items())[0]
        # result = f"'{key}':'{value}'"
        spreadsheet_token = self.sheet_ids.get(key).split('_')[0]
        range_str = f"{self.sheet_ids.get(key).split('_')[1]}!{value.split(':')[0]}:{value.split(':')[1]}"
        ele_value = self.get_sheets_datas(spreadsheet_token, range_str) 
        return list(map(lambda x:x[0],ele_value))

    # new
    def update_text_yy_content(self, element):
        pattern = r"「(?:[^「」]|「[^「」]*」)*」"
        # pattern = r"¥¥"
        # 查找匹配项
        matches = re.findall(pattern, element['text_run']['content']) ## 根据正则获取需要替换的内容
        if matches:
            print('------')
            print(matches)
            for matche in matches:
                re_str = copy.deepcopy(matche) 
                matche = matche.replace('「','{').replace('」','}')
                jsond = eval(matche)
                print(matche)
                print(jsond['index'])
                print(type(jsond['index']))
                if jsond['type'] == '>':
                    key,value = list(jsond['index'].items())[0]
                    index = self.get_sheet_data_from_kv(key,value)
                    key,value = list(jsond['data'].items())[0]
                    data = self.get_sheet_data_from_kv(key,value)
                    df = pd.DataFrame({'index':index,'data':data})
                    drop = jsond['drop'] if 'drop' in jsond else ' '
                    df = df[~df['index'].str.contains(drop,regex=True)] # 过滤逻辑
                    df.sort_values(by='data',ascending=False,inplace=True)
                    restext = ' > '.join(df['index'].values.tolist())
                    element['text_run']['content'] = element['text_run']['content'].replace(re_str, restext)
                    print(index,data,restext)
                elif jsond['type'] == '<':
                    key,value = list(jsond['index'].items())[0]
                    index = self.get_sheet_data_from_kv(key,value)
                    key,value = list(jsond['data'].items())[0]
                    data = self.get_sheet_data_from_kv(key,value)
                    df = pd.DataFrame({'index':index,'data':data})
                    drop = jsond['drop'] if 'drop' in jsond else ' '
                    df = df[~df['index'].str.contains(drop,regex=True)] # 过滤逻辑
                    df.sort_values(by='data',ascending=True,inplace=True)
                    restext = ' < '.join(df['index'].values.tolist())
                    element['text_run']['content'] = element['text_run']['content'].replace(re_str, restext)
                    print(index,data,restext)
                elif 'S' in jsond['type']:
                    topn,ascending = int(jsond['type'].split('_')[1]),jsond['type'].split('_')[2]
                    key,value = list(jsond['index'].items())[0]
                    index = self.get_sheet_data_from_kv(key,value)
                    key,value = list(jsond['data'].items())[0]
                    data = self.get_sheet_data_from_kv(key,value)
                    df = pd.DataFrame({'index':index,'data':data})
                    drop = jsond['drop'] if 'drop' in jsond else ' '
                    print('drop',drop)
                    df = df[~df['index'].str.contains(drop,regex=True)] # 过滤逻辑
                    df['data'] = df['data'].map(lambda x:eval(x.replace('%',''))/100)
                    print(matche,ascending)
                    print('df',df)
                    if ascending == '+':
                        df = df[df['data']>0]
                    elif ascending == '-':
                        df = df[df['data']<0]
                    # df.sort_values(by='data',ascending=True,inplace=True)
                    if len(df)>0:
                        restext = '、'.join(df['index'].values.tolist()[:topn])
                    else:
                        restext = '无群组'    
                    element['text_run']['content'] = element['text_run']['content'].replace(re_str, restext)
                    print(index,data,restext)
            return True
    
    # 更新block的动作
    def update_block(self, block_id, update_text_elements):
        url = f"{self.url}/docx/v1/documents/{self.doc_token}/blocks/{block_id}"
        params = {
            "document_revision_id": "-1",
            "user_id_type": "user_id"
        }
        headers = {
            "Content-Type": "application/json",
            "Authorization": "Bearer " + self.user_token,
        }
        data = {
            "update_text_elements": {
                "elements": update_text_elements
            }
        }
        response = requests.patch(url, headers=headers, params=params, json=data)
        # print(response.status_code)
        print("update result",response.text)

    # 获取文档中表格的数据
    def get_sheets_data(self, spreadsheetToken,range_str):
        url = f"{self.url}/sheets/v2/spreadsheets/{spreadsheetToken}/values/{range_str}"
        headers = {
            "Authorization": "Bearer " + self.user_token,
            "Content-Type": "application/json; charset=utf-8"
        }
        params = {
            "valueRenderOption": "FormattedValue",
        }
        response = requests.get(url, headers=headers, params=params)
        # print(response.text)
        res = response.json()
        return res['data']['valueRange']['values']
    
    # 将字符串转换成数字
    def convert_to_float(self,s, none_to_zero=False):
        if isinstance(s, str):
            s = s.replace(',', '') # 千分位处理
            if s.endswith('%'):
                # 处理百分数
                try:
                    return float(s.rstrip('%')) / 100
                except ValueError:
                    return 0 if none_to_zero else None
            try:
                # 尝试转换为浮点数
                return float(s)
            except ValueError:
                # 无法转换，返回 0
                return 0 if none_to_zero else None
        elif isinstance(s, (int, float)):
            return float(s)
        return 0 if none_to_zero else None

    # update_text_run_content 中的 匹配月份
    def update_text_run_content_template_01(self, element):
        pattern = r"\{month\}"
        matches = re.findall(pattern, element['text_run']['content']) ## 根据正则获取需要替换的内容
        if matches:
            # print(matches)
            for key in matches:
                element['text_run']['content'] = element['text_run']['content'].replace("{month}", str(self.month))
            return True
        return False
    
    # update_text_run_content 中的 匹配{'表格5':'K3'}
    def update_text_run_content_template_02(self, element):
        pattern = r"\{'([表格\d+']+)':'([^']+)'\}"
        input_str = element['text_run']['content']
        matches = re.findall(pattern, element['text_run']['content']) ## 根据正则获取需要替换的内容
        if matches:
            # print(matches)
            for key, value in matches:
                result = f"'{key}':'{value}'"
                # print(result, self.sheet_ids)
                spreadsheet_token = self.sheet_ids.get(key).split('_')[0]
                range_str = f"{self.sheet_ids.get(key).split('_')[1]}!{value}:{value}"
                ele_value = self.get_sheets_data(spreadsheet_token, range_str)[0][0] ## 获取表格数据用于替换正则匹配的内容
                # print(ele_value)
                element['text_run']['content'] = element['text_run']['content'].replace("{"+f"{result}" + "}", str(ele_value))
            return True
        return False
    
    # update_text_run_content 中的 匹配 {'表格5':'A3:D8','D','A',0,'D#群组|商安|运营支持中心|DMC',0}
    def update_text_run_content_template_03(self, element):
        pattern = r"\{'([^']+)':'([^']+)','([^,]+)','([^,]+)',(\d+),'([^,]+)',(\d+)\}"
        matches = re.findall(pattern, element['text_run']['content'])
        if matches:
            for values in matches:
                key, value, sort_base, value_base, num, flt, reverse = values
                spreadsheet_token = self.sheet_ids.get(key).split('_')[0]
                range_str = f"{self.sheet_ids.get(key).split('_')[1]}!{value}"
                sheets_data = self.get_sheets_data(spreadsheet_token, range_str)
                # print(ele_value)
                column, keyword = flt.split('#')
                print(key, value, sort_base, value_base, num, flt, reverse)
                # 获取列索引
                column_mapping = {**{chr(65 + i): i for i in range(26)}, **{chr(65 + i) + chr(65 + j): 26 + i * 26 + j for i in range(26) for j in range(26) if 26 + i * 26 + j < 100}}
                column_index = column_mapping.get(sort_base)
                kw_n= flt.split('#')[0]
                kw_column_index = column_mapping.get(kw_n)
                kws = flt.split('#')[1].split('|')          
                # 筛选包含关键字的行
                filtered_data = [row for row in sheets_data if any(kw in str(row[kw_column_index]) for kw in kws)]
                # 排序顺序
                reverse_order = int(reverse) != 0
                sorted_data = sorted(filtered_data, key=lambda x: self.convert_to_float(x[column_index],True), reverse=reverse_order)
                result_column_index = column_mapping.get(value_base)
                repl = [row[result_column_index] for row in sorted_data]
                print(repl)
                element['text_run']['content'] = element['text_run']['content'].replace("{"+f"'{key}':'{value}','{sort_base}','{value_base}',{num},'{flt}',{reverse}" + "}", str(repl[int(num)]))
                # element['text_run']['content'] = re.sub(pattern, str(repl[int(num)]), element['text_run']['content'])
            return True
        return False
    
    # update_text_run_content 中的 匹配 {'表格3':'A6:H15','H','A','A#群组|商安-|中心|DMC','模版1'}
    def update_text_run_content_template_04(self, element):
        pattern = r"\{'([^']+)':'([^']+)','([^,]+)','([^,]+)','([^,]+)','(模版\d+)'\}"
        matches = re.findall(pattern, element['text_run']['content'])
        has_mod = False
        if matches:
            for values in matches:
                key, value, sort_base, value_base, flt, temp = values
                spreadsheet_token = self.sheet_ids.get(key).split('_')[0]
                range_str = f"{self.sheet_ids.get(key).split('_')[1]}!{value}"
                sheets_data = self.get_sheets_data(spreadsheet_token, range_str)
                # print(ele_value)
                column, keyword = flt.split('#')
                # print(key, value, sort_base, value_base, flt, temp)
                # 获取列索引
                column_mapping = {**{chr(65 + i): i for i in range(26)}, **{chr(65 + i) + chr(65 + j): 26 + i * 26 + j for i in range(26) for j in range(26) if 26 + i * 26 + j < 100}}
                column_index = column_mapping.get(sort_base)
                kw_n= flt.split('#')[0]
                kw_column_index = column_mapping.get(kw_n)
                kws = flt.split('#')[1].split('|')          
                # 筛选包含关键字的行
                filtered_data = [row for row in sheets_data if any(kw in str(row[kw_column_index]) for kw in kws)]
                # 模版1
                if temp == '模版1':
                    # 抖音、垂直、运营支持、DMC人审量级环比增长，其他组织下降
                    result_column_index = column_mapping.get(value_base)
                    up = []
                    dn = []
                    for a in filtered_data:
                        if self.convert_to_float(a[column_index]) and self.convert_to_float(a[column_index])>0:
                            up.append(a[result_column_index])
                        if self.convert_to_float(a[column_index]) and self.convert_to_float(a[column_index])<0:
                            dn.append(a[result_column_index])
                    ans = []
                    if up:
                        ans.append(f"{'、'.join(up)}人审量级环比增长")
                    if dn:
                        ans.append(f"{'、'.join(dn)}人审量级环比下降")
                    if ans:
                        has_mode = True
                        element['text_run']['content'] = element['text_run']['content'].replace("{"+f"'{key}':'{value}','{sort_base}','{value_base}','{flt}','{temp}'" + "}", ','.join(ans))
                elif temp == '模版2':
                    # 抖音、垂直、运营支持、DMC人审量级环比增长，其他组织下降
                    result_column_index = column_mapping.get(value_base)
                    dn = []
                    for a in filtered_data:
                        if self.convert_to_float(a[column_index]) and self.convert_to_float(a[column_index])<0:
                            dn.append(a[result_column_index])
                    ans = []
                    if dn:
                        ans.append(f"{'、'.join(dn)}未达成目标")
                    if ans:
                        has_mod = True
                        element['text_run']['content'] = element['text_run']['content'].replace("{"+f"'{key}':'{value}','{sort_base}','{value_base}','{flt}','{temp}'" + "}", '，'+(','.join(ans)))
        return has_mod

    # update_text_run_content 中的 匹配 |eval(10 / 20)| 并计算
    def update_text_run_content_template_05(self, element):
        matches,i,text = [],0,element['text_run']['content']
        while i < len(text):
            # 查找 eval( 的起始位置
            if text[i:i + 5] == "eval(":
                start = i + 5
                stack = []
                j = start
                while j < len(text):
                    if text[j] == '(':
                        stack.append('(')
                    elif text[j] == ')':
                        if stack:
                            stack.pop()
                        else:
                            # 栈为空，说明找到了对应的右括号
                            matches.append(text[start:j])
                            break
                    j += 1
                i = j
            else:
                i += 1

        if matches:
            for key in matches:
                try:
                    key_real = key.replace(',', '')
                    num = eval(key_real)
                    print(num)
                    if isinstance(num, int):
                        ele_value = num
                    elif isinstance(num, float):
                        ele_value = round(num, 2)
                    else:
                        ele_value = num
                    element['text_run']['content'] = element['text_run']['content'].replace(f"eval({key})", str(ele_value))  
                except Exception as e:
                    print(e)
                    element['text_run']['content'] = element['text_run']['content'].replace(f"eval({key})", '-')
            return True
        return False

    # 更新节点内容，根据模版匹配并更新内容
    def update_text_run_content(self, element):
        has_mod = False
        if 'text_run' in element:
            try:
                # 匹配月份
                if self.update_text_run_content_template_01(element):
                    has_mod = True  

                # 匹配 {'表格5':'K3'}
                if self.update_text_run_content_template_02(element):
                    has_mod = True

                # 匹配  {'表格5':'A3:D8','D','A',0,'D#群组|商安|运营支持中心|DMC',0}
                if self.update_text_run_content_template_03(element):
                    has_mod = True

                # 模版 匹配 {'表格3':'A6:H15','H','A','A#群组|商安-|中心|DMC','模版1'}
                if self.update_text_run_content_template_04(element):
                    has_mod = True

                # 匹配 |eval(10 / 20)| 并计算
                if self.update_text_run_content_template_05(element):
                    has_mod = True

                # 修改YY
                if self.update_text_yy_content(element):
                    has_mod = True
                
                if self.upload_text_rag_block(element):
                    has_mod = True

            except Exception as e:
                print(e,element)
        return has_mod

    def upload_text_rag_block(self,element):


        print('---',element,'----')
        print(element['text_run']['content'])
        pattern = r"({.*?})"        # 查找匹配项
        matches = re.findall(pattern, element['text_run']['content']) ## 根据正则获取需要替换的内容

        user_data = load_user_data(self.user_id,user_data_dir)
        group2file_path=user_data.get('group2file_path',{})
        if len(group2file_path)==0:
            print('获取group2file_path失败',self.user_id,user_data_dir)
            return False
        if matches:
            print(matches)
            for matche in matches:
                re_str = copy.deepcopy(matche) 
                print(re_str)
                jsond = eval(matche)
                if 'RAG' in jsond.keys():
                    if "各个群组" in jsond['RAG']:
                        main_query = jsond['RAG'].replace("各个群组","")
                        group2data={}
                        for group in ['抖音群组',"直播群组",'头条群组','垂直群组','广告群组','生服群组','电商群组','运营支持中心','DMC']:
                            
                            file_path = group2file_path.get(group,'')
                            with open(file_path, 'r', encoding='utf-8') as f:
                                report = f.read()
                            prompt ="从以下的markdown文件中提取{0}，无需进行分析，直接提取文件中的原因描述。\n 文件内容如下：\n{1}".format(main_query,report)
                            llm_result = llm.chat(prompt)
                            print(group,llm_result)
                            group2data[group] = llm_result
                            print('--'*20)

                        prompt="用简单的文本总结{0}，对于没有答案的群组，则不进行总结。每个群组尽量用一句话描述(提取最重要的原因)，最后使用不换行的一句话进行输出。群组以及原因如下：\n".format(jsond['RAG'])
                        for k,v  in group2data.items():
                            prompt+=k+":"+v+'\n'

                        llm_result = llm.chat(prompt)
                        print(llm_result)   
                        element['text_run']['content'] = element['text_run']['content'].replace(re_str, llm_result)  
                        return True
                    
                    else:
                        main_query = jsond['RAG']
                        for group in ['抖音群组',"直播群组",'头条群组','垂直群组','广告群组','生服群组','电商群组','运营支持中心','DMC','商安-电商','商安-广告经营','商安-生活服务']:
                            if group in element['text_run']['content']:
                                if group=='商安-电商':
                                    group='电商群组'
                                elif group=='商安-广告经营':
                                    group='广告群组'
                                elif group=='商安-生活服务':
                                    group='生服群组'
                                file_path = group2file_path.get(group,'')
                                with open(file_path, 'r', encoding='utf-8') as f:
                                    report = f.read()
                                prompt ="从以下的markdown文件中提取{0}的原因，无需进行分析，直接提取文件中的原因描述。将主要原因总结为一句话输出（不要换行）\n 文件内容如下：\n{1}".format(main_query,report)
                                llm_result = llm.chat(prompt)
                                print(group,llm_result)
                                element['text_run']['content'] = element['text_run']['content'].replace(re_str, llm_result)
                                return True
                        
                        if "商业安全-合计" in element['text_run']['content']:
                            group2data={}
                            for group in ['广告群组','生服群组','电商群组']:
                                file_path = group2file_path.get(group,'')
                                with open(file_path, 'r', encoding='utf-8') as f:
                                    report = f.read()
                                prompt ="从以下的markdown文件中提取{0}，无需进行分析，直接提取文件中的原因描述。\n 文件内容如下：\n{1}".format(main_query,report)
                                llm_result = llm.chat(prompt)
                                print(group,llm_result)
                                group2data[group] = llm_result
                                print('--'*20)

                            prompt="用简单的文本总结{0}，对于没有答案的群组，则不进行总结。每个群组尽量用一句话描述(提取最重要的原因)，最后使用不换行的一句话进行输出。群组以及原因如下：\n".format(jsond['RAG'])
                            for k,v  in group2data.items():
                                prompt+=k+":"+v+'\n'

                            llm_result = llm.chat(prompt)
                            print(llm_result)   
                            element['text_run']['content'] = element['text_run']['content'].replace(re_str, llm_result)  
                            return True
                        

        return False

    # 记录修改到的节点
    def mark_mod_block_id(self, block_id):
        try:
            file_name = f"{self.user_id}#block_id" # self.user_id + "#doc#" + self.doc_token
            save_user_data(file_name, self.user_data_dir, {
                "block_id": block_id
            })
        except Exception as e:
            print(f"记录block id 错误：{e}")

    # 处理block节点（根据类型去取数所有节点然后去具体的元素），取数之后调用节点更新函数更新节点
    def process_block(self, block,block_types):
        for block_type in block_types: 
            if block_type in block and 'elements' in block[block_type]:
                has_mod = False
                for element in block[block_type]['elements']:
                    mod_block = self.update_text_run_content(element)
                    if mod_block:
                        has_mod = True
                if has_mod:
                    # print(block,end='\n\n\n') # ,block[block_type]['elements']
                    self.update_block(block['block_id'], block[block_type]['elements'])
                    self.mark_mod_block_id(block['block_id'])


    # 入口函数，循环遍历每个节点
    def modify_content(self):
        data = copy.deepcopy(self.blocks)
        block_types = set()
        for block in data:
            for key in block.keys():
                if isinstance(block[key], dict) and 'elements' in block[key]:
                    block_types.add(key)
        
        def _modify(data, all_data):
            for item in data:
                self.process_block(item,block_types)
                if 'children' in item:
                    child_data = [d for d in all_data if d['block_id'] in item['children']]
                    _modify(child_data, all_data)
            return data

        return _modify(data, data)




    