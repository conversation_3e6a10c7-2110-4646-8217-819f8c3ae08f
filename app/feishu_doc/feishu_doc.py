import json
import os
from pathlib import Path
import yaml
from typing import Optional, Dict, List, Any
from dataclasses import dataclass, field, asdict

import lark_oapi as lark
from lark_oapi.api.docx.v1 import *
from lark_oapi.api.docs.v1 import GetContentRequest, GetContentResponse
from app.utils.config_loader import config

@dataclass
class SheetInfo:
    """表格信息数据类"""
    block_id: str
    token: str
    parent_id: str
    title: str = ''
    content: List[str] = None
    content_block_ids: List[str] = None
    content_block_info: List[Dict] = None
    
    def __post_init__(self):
        # 确保列表字段不为None
        if self.content is None:
            self.content = []
        if self.content_block_ids is None:
            self.content_block_ids = []
        if self.content_block_info is None:
            self.content_block_info = []
    
    def to_dict(self):
        """将对象转换为字典"""
        return {
            'block_id': self.block_id,
            'token': self.token,
            'parent_id': self.parent_id,
            'title': self.title,
            'content': self.content,
            'content_block_ids': self.content_block_ids,
            'content_block_info': self.content_block_info
        }
    
    def to_json(self, indent: int = 4) -> str:
        """将对象转换为JSON字符串"""
        return json.dumps(self.to_dict(), ensure_ascii=False, indent=indent)
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'SheetInfo':
        """从字典创建对象"""
        return cls(**data)
    
    @classmethod
    def from_json(cls, json_str: str) -> 'SheetInfo':
        """从JSON字符串创建对象"""
        return cls.from_dict(json.loads(json_str))



def get_docid_from_url(url: str) -> str:
    """从URL中提取文档ID"""
    return url.split('/')[-1]




def create_feishu_client(app_token: bool = True) -> lark.Client:
    """创建飞书客户端"""
    builder = lark.Client.builder().log_level(lark.LogLevel.DEBUG)
    if app_token:
        return builder.app_id(config.get('feishu.app_id')).app_secret(config.get('feishu.app_secret')).build()
    return builder.enable_set_token(True).build()



def get_feishu_blocks_app_token(doc_id: str) -> Optional[str]:
    """获取飞书文档内容（使用应用身份访问）"""
    try:
        client = create_feishu_client()
        request = ListDocumentBlockRequest.builder() \
            .document_id(doc_id) \
            .page_size(500) \
            .document_revision_id(-1) \
            .build()

        response: ListDocumentBlockResponse = client.docx.v1.document_block.list(request)
        
        if not response.success():
            error_msg = f"获取文档失败: code={response.code}, msg={response.msg}, log_id={response.get_log_id()}"
            lark.logger.error(error_msg)
            return None

        return lark.JSON.marshal(response.data, indent=4)
    
    except Exception as e:
        lark.logger.error(f"处理文档时发生异常: {str(e)}")
        return None

def get_feishu_doc_blocks_user_token(doc_id: str, user_token: str) -> Optional[str]:
    """获取飞书文档内容（使用用户身份访问）"""
    try:
        client = create_feishu_client(app_token=False)
        request = ListDocumentBlockRequest.builder() \
            .document_id(doc_id) \
            .page_size(500) \
            .document_revision_id(-1) \
            .build()


          # 发起请求
        option = lark.RequestOption.builder().user_access_token(user_token).build()
        response: ListDocumentBlockResponse = client.docx.v1.document_block.list(request, option)

        if not response.success():
            error_msg = f"获取文档内容失败: code={response.code}, msg={response.msg}, log_id={response.get_log_id()}"
            lark.logger.error(error_msg)
            return None

        return lark.JSON.marshal(response.data, indent=4)
    
    except Exception as e:
        lark.logger.error(f"处理文档内容时发生异常: {str(e)}")
        return None

def extract_text_content(block: Dict) -> str:
    """从块中提取文本内容"""
    if 'ordered' in block:
        elements = block['ordered']['elements']
    elif 'text' in block:
        elements = block['text']['elements']
    else:
        return ''
    
    return ''.join([
        info['text_run']['content'] 
        for info in elements 
        if 'text_run' in info
    ])

def parse_doc_file(file_content: str) -> List[SheetInfo]:
    """解析飞书文档文件内容"""
    try:
        doc_data = json.loads(file_content)
        items = doc_data['items']
        
        # 获取所有表格信息
        sheet_list = [
            SheetInfo(
                block_id=item['block_id'],
                token=item['sheet']['token'],
                parent_id=item['parent_id']
            )
            for item in items
            if 'sheet' in item
        ]

        # 处理每个表格
        for sheet_info in sheet_list:
            content_block_ids = []
            content_block_info = []
            content = []

            # 获取表格标题和内容块ID
            for item in items:
                if item['block_id'] == sheet_info.parent_id:
                    for key in item:
                        if key in ['text', 'page'] or key.startswith('heading'):
                            sheet_info.title = item[key]['elements'][0]['text_run']['content']
                            break
                
                if (item['parent_id'] == sheet_info.parent_id and 
                    item['block_id'] != sheet_info.block_id and 
                    'children' in item):
                    content_block_ids.extend(item['children'])

            # 处理内容块
            for item in items:
                if item['block_id'] in content_block_ids:
                    text_content = extract_text_content(item)
                    if text_content:
                        content.append(text_content)
                    
                    if 'children' in item:
                        content_block_ids.extend(item['children'])
                    
                    content_block_info.append(item)

            sheet_info.content = content
            sheet_info.content_block_ids = content_block_ids
            sheet_info.content_block_info = content_block_info

        return sheet_list

    except Exception as e:
        lark.logger.error(f"解析文档内容时发生异常: {str(e)}")
        return []


  