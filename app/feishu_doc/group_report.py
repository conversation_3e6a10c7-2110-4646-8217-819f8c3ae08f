import json
import os
from pathlib import Path
import yaml
from typing import Optional, Dict, List, Any, Union
from dataclasses import dataclass, field, asdict
import logging
import pandas as pd
from itertools import accumulate

import lark_oapi as lark
from lark_oapi.api.docx.v1 import *
from lark_oapi.api.docs.v1 import GetContentRequest, GetContentResponse
from app.utils.config_loader import config
from app.feishu_sheet.feishu_sheet import group_report_status_check, read_feishu_sheet_user_token
from app.feishu_doc.feishu_doc import get_feishu_doc_blocks_user_token

# 设置日志记录
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# 设置数据目录
base_dir = Path(__file__).parent.parent.parent
data_dir = base_dir / 'data'
user_data_dir = data_dir / 'users'
user_data_dir.mkdir(parents=True, exist_ok=True)


def extract_text_run(data: Union[Dict, List]) -> List[str]:
    """
    递归提取文档中的文本内容
    
    Args:
        data: 飞书文档数据结构
        
    Returns:
        提取的文本内容列表
    """
    results = []

    def recurse(obj: Union[Dict, List]) -> None:
        if isinstance(obj, dict):
            if "text_run" in obj:
                results.append(obj["text_run"]["content"])
            for value in obj.values():
                recurse(value)
        elif isinstance(obj, list):
            for item in obj:
                recurse(item)

    recurse(data)
    return results


def group_report2md(user_token: str,group_report_url:str):
    """
    将飞书群组报告转换为Markdown格式
    
    Args:
        user_token: 用户认证token
        
    Returns:
        生成的markdown文件路径列表
    """

    logger.info(f"Processing report: {group_report_url}")
            
    try:
        doc_id = group_report_url.split('/')[-1]
        doc_blocks = get_feishu_doc_blocks_user_token(doc_id, user_token)
        doc_data = json.loads(doc_blocks)['items']

        markdown_content = []
        title = doc_data[0]['page']['elements'][0]['text_run']['content']
        for name in ['抖音群组',"直播群组",'头条群组','垂直群组','广告群组','生服群组','电商群组','运支','DMC']:
            if name in title:
                title = '{0}月报'.format(name)


        logger.info(f"report title : {title}")
                
        # 处理文档块
        for block_info in doc_data:
            if block_info['block_type'] in (30, 31):  # 表格类型
                sheet_token = block_info['sheet']['token']
                sheet_content = read_feishu_sheet_user_token(sheet_token, user_token)
                values = sheet_content['data']['valueRange']['values']
                
                if values:
                    # if '总人力' in values[0]:
                    #     df = pd.DataFrame(values[2:], columns=values[1])
                    # else:

                    filled_lst = list(accumulate(values[0], lambda x, y: y if y is not None else x))
                    df = pd.DataFrame(values[1:], columns=filled_lst)
                    df = df[~df.iloc[:, 0].astype(str).str.startswith("口径备注")]
                    if '偏离分析描述' in df.columns:
                        df = df.drop(columns=['偏离分析描述'])
                    # df = df.fillna(method='ffill')

                    if df.shape[0]<=1:
                        continue
                    df.iloc[0] = df.iloc[0].ffill()
                    df.iloc[:, 0] = df.iloc[:, 0].ffill()


                    markdown_content.append(df.to_markdown(index=False))
            else:
                text = extract_text_run(block_info)
                if text:
                    markdown_content.append(''.join(text))

        # 保存为markdown文件
        file_path = user_data_dir / f"{title}.md"
        logger.info(f"markdown file path : {file_path}")
        with open(file_path, 'w', encoding='utf-8') as fw:
            fw.write('\n'.join(markdown_content))
        logger.info(f"Successfully generated markdown file: {file_path}")
                
    except Exception as e:
        logger.error(f"Error processing document {group_report_url}: {str(e)}")
        
    return str(file_path)



def save_group_report(user_token: str,group_report_url:str):
    """
    将飞书群组报告转换为Markdown格式
    
    Args:
        user_token: 用户认证token
        
    Returns:
        生成的markdown文件路径列表
    """

    logger.info(f"Processing report: {group_report_url}")
            
    try:
        doc_id = group_report_url.split('/')[-1]
        doc_blocks = get_feishu_doc_blocks_user_token(doc_id, user_token)
        doc_data = json.loads(doc_blocks)['items']

        markdown_content = []
        
        title = doc_data[0]['page']['elements'][0]['text_run']['content']
        if len(title)<=1:
            title = doc_data[0]['page']['elements'][1]['text_run']['content']
        print(doc_id,title)
        print(doc_data[0]['page']['elements'][0]['text_run'])
        group_name = ''
        for name in ['抖音群组',"直播群组",'头条群组','垂直群组','广告群组','生服群组','电商群组','运营支持中心','DMC']:
            if name in title:
                group_name = name
                title = '{0}月报'.format(name)

        file_path = user_data_dir / f"{title}.md"
        if os.path.exists(file_path) and os.path.getsize(file_path) > 0:
            return str(file_path),group_name


                
        # 处理文档块
        for block_info in doc_data:
            if block_info['block_type'] in (30, 31) and 'sheet' in block_info:  # 表格类型
                sheet_token = block_info['sheet']['token']
                sheet_content = read_feishu_sheet_user_token(sheet_token, user_token)
                values = sheet_content['data']['valueRange']['values']
                
                if values:
                    # if '总人力' in values[0]:
                    #     df = pd.DataFrame(values[2:], columns=values[1])
                    # else:

                    filled_lst = list(accumulate(values[0], lambda x, y: y if y is not None else x))
                    df = pd.DataFrame(values[1:], columns=filled_lst)
                    df = df[~df.iloc[:, 0].astype(str).str.startswith("口径备注")]
                    # if '偏离分析描述' in df.columns:
                    #     df = df.drop(columns=['偏离分析描述'])
                    # df = df.fillna(method='ffill')

                    if df.shape[0]<=1:
                        continue
                    df.iloc[0] = df.iloc[0].ffill()
                    df.iloc[:, 0] = df.iloc[:, 0].ffill()


                    markdown_content.append(df.to_markdown(index=False))
            else:
                text = extract_text_run(block_info)
                if text:
                    markdown_content.append(''.join(text))

        # 保存为markdown文件
        
        logger.info(f"markdown file path : {file_path}")
        with open(file_path, 'w', encoding='utf-8') as fw:
            fw.write('\n'.join(markdown_content))
        logger.info(f"Successfully generated markdown file: {file_path}")
                
    except Exception as e:
        logger.error(f"Error processing document {group_report_url}: {str(e)}")
        
    return str(file_path),group_name