import time
import base64
import logging
from dataclasses import dataclass
from typing import Optional, List, Dict, Any
from threading import Lock
from openai import OpenAI, OpenAIError
from app.utils.config_loader import config


@dataclass
class TokenUsage:
    """Token使用统计数据类"""
    prompt_tokens: int = 0
    completion_tokens: int = 0
    
    def add(self, prompt: int, completion: int) -> None:
        self.prompt_tokens += prompt
        self.completion_tokens += completion
        
    def calculate_cost(self) -> tuple[float, float, float]:
        """计算成本"""
        input_cost = (self.prompt_tokens/1000) * 0.0032
        output_cost = (self.completion_tokens/1000) * 0.0063
        return input_cost, output_cost, input_cost + output_cost

class LLM:
    """LLM客户端封装类,用于与OpenAI API交互"""
    
    # 系统提示语
    SYSTEM_PROMPT = "你是豆包。你需要以专业、严谨、准确的方式回答用户的问题，确保回答既具帮助性又保持客观中立。"
    
    def __init__(self) -> None:
        """初始化LLM客户端"""
        self.api_key = config.get('llm.api_key')
        self.base_url = config.get('llm.base_url')
        self.model_name = config.get('llm.model_name')
        
        # 初始化OpenAI客户端
        self.client = OpenAI(api_key=self.api_key, base_url=self.base_url)
        
        # Token统计
        self._token_usage = TokenUsage()
        self._lock = Lock()
        
        logging.info(f"LLM initialized with model: {self.model_name}")

    def _make_request(self, messages: List[Dict[str, str]], model: Optional[str] = None, 
                     retries: int = 3, retry_delay: int = 1) -> Optional[str]:
        """统一的API请求处理"""
        for attempt in range(retries):
            try:
                completion = self.client.chat.completions.create(
                    model=model or self.model_name,
                    messages=messages,
                    temperature=0,
                    top_p=0.99
                )
                
                with self._lock:
                    self._token_usage.add(
                        completion.usage.prompt_tokens,
                        completion.usage.completion_tokens
                    )
                    
                return completion.choices[0].message.content
                
            except OpenAIError as e:
                logging.error(f"OpenAI Error (attempt {attempt+1}/{retries}): {e}")
                if attempt < retries - 1:
                    time.sleep(retry_delay)
            except Exception as e:
                logging.error(f"Unexpected error: {e}")
                return None
        return None

    def generate(self, prompt: str, llm_model_name: Optional[str] = None) -> Optional[str]:
        """生成回复,带系统提示"""
        messages = [
            {"role": "system", "content": self.SYSTEM_PROMPT},
            {"role": "user", "content": prompt}
        ]
        return self._make_request(messages, llm_model_name)


    def generate_with_images(self, prompt: str, image_urls: List[str], 
                           llm_model_name: Optional[str] = None, max_image: int = 24) -> Optional[str]:
        """生成带图片的回复，默认是高规格图片"""

        prompt_data = [{"type": "text", "text": prompt}]
        for image_url in image_urls[:max_image]:
            prompt_data.append({
                "type": "image_url",
                "image_url": {
                    "url": image_url,
                    "detail": "high"

                }
            })

        messages = [
            {"role": "system", "content": self.SYSTEM_PROMPT},
            {"role": "user", "content": prompt_data}
        ]
        return self._make_request(messages, llm_model_name)

    def chat(self, message: str, llm_model_name: Optional[str] = None) -> Optional[str]:
        """简单对话接口"""
        messages = [{"role": "user", "content": message}]
        return self._make_request(messages, llm_model_name, retries=10)

    def print_cost(self) -> None:
        """打印token使用量和费用统计"""
        input_cost, output_cost, total_cost = self._token_usage.calculate_cost()
        print(
            f"Token usage - Prompt: {self._token_usage.prompt_tokens}, "
            f"Completion: {self._token_usage.completion_tokens}\n"
            f"Cost breakdown:\n"
            f" Input: ${input_cost:.4f}\n"
            f" Output: ${output_cost:.4f}\n"
            f" Total: ${total_cost:.4f}"
        )


llm = LLM()