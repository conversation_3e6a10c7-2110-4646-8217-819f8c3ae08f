import requests
import json
import os
from typing import Optional
from pathlib import Path


# 获取访问令牌
def get_token(app_id: str, app_secret: str) -> str:
    """
    获取飞书访问令牌
    
    Args:
        app_id: 应用ID
        app_secret: 应用密钥
    
    Returns:
        str: 访问令牌
    """
    url = "https://open.feishu.cn/open-apis/auth/v3/tenant_access_token/internal/"
    payload = {
        "app_id": app_id,
        "app_secret": app_secret
    }
    
    try:
        response = requests.post(url, json=payload)
        response.raise_for_status()
        return response.json()['tenant_access_token']
    except requests.exceptions.RequestException as e:
        raise Exception(f"获取访问令牌失败: {str(e)}")


def get_user_token(user_id: str) -> str:
    """
    从用户配置文件获取用户token
    
    Args:
        user_id: 用户ID
    
    Returns:
        str: 用户token，如果文件不存在返回空字符串
    """
    try:
        # 使用 Path 对象处理路径，更加安全和可靠
        base_dir = Path(__file__).parent.parent.parent.parent
        user_file_path = base_dir / 'data' / 'users' / f"{user_id}.json"
        
        if not user_file_path.exists():
            return ''
            
        with user_file_path.open('r', encoding='utf-8') as f:
            user_data = json.load(f)
            return user_data.get('user_token', '')
    except (json.JSONDecodeError, OSError) as e:
        # 记录错误但返回空字符串作为默认值
        print(f"读取用户token失败: {str(e)}")
        return ''
