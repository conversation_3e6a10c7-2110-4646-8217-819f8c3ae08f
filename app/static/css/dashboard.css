/* 全局样式 */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
    font-family: 'SF Pro Display', 'SF Pro Text', 'Noto Sans SC', -apple-system, BlinkMacSystemFont, 'Helvetica Neue', 'Arial', sans-serif;
    font-size: 14px; /* 调整全局基础字体大小 */
}

html, body {
    height: 100%;
    width: 100%;
    overflow: hidden;
    margin: 0;
    padding: 0;
}

body {
    background-color: #f5f5f7;
    color: #1d1d1f;
    line-height: 1.5;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
}

.container {
    display: flex;
    height: 100vh;
    width: 100vw;
    overflow: hidden;
    /* 移除了max-width和margin限制 */
    box-shadow: none;
}

/* 左侧对话区域 */
.chat-container {
    width: 35%;
    display: flex;
    flex-direction: column;
    border-right: 1px solid #d2d2d7;
    background-color: #fff;
}

.chat-header {
    padding: 20px;
    border-bottom: 1px solid #d2d2d7;
    background-color: rgba(255, 255, 255, 0.8);
    backdrop-filter: blur(20px);
    -webkit-backdrop-filter: blur(20px);
    position: sticky;
    top: 0;
    z-index: 10;
}

.chat-header h2 {
    font-size: 18px; /* 从20px减小到18px */
    font-weight: 600;
    color: #1d1d1f;
    letter-spacing: -0.02em;
}

.chat-messages {
    flex: 1;
    overflow-y: auto;
    padding: 20px;
    background-color: #fbfbfd;
}

.message {
    margin-bottom: 20px;
    display: flex;
    flex-direction: column;
}

.message.user {
    align-items: flex-end;
}

.message.system {
    align-items: flex-start;
}

.message-content {
    max-width: 85%;
    padding: 12px 16px;
    border-radius: 18px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
    transition: all 0.3s ease;
}

.user .message-content {
    background-color: #0071e3;
    color: white;
}

.system .message-content {
    background-color: #f5f5f7;
    color: #1d1d1f;
    border: 1px solid #e5e5e5;
}

.message-content p {
    margin: 0;
    line-height: 1.5;
    font-size: 13px; /* 添加消息内容的字体大小 */
}

.chat-input {
    padding: 16px 20px;
    border-top: 1px solid #d2d2d7;
    display: flex;
    align-items: center;
    background-color: rgba(255, 255, 255, 0.8);
    backdrop-filter: blur(20px);
    -webkit-backdrop-filter: blur(20px);
}

.chat-input textarea {
    flex: 1;
    height: 50px;
    padding: 12px 16px;
    border: 1px solid #d2d2d7;
    border-radius: 12px;
    resize: none;
    outline: none;
    font-size: 14px; /* 从15px减小到14px */
    transition: all 0.3s ease;
    background-color: #f5f5f7;
}

.chat-input textarea:focus {
    border-color: #0071e3;
    box-shadow: 0 0 0 3px rgba(0, 113, 227, 0.2);
}

.chat-input button {
    margin-left: 12px;
    width: 40px;
    height: 40px;
    border: none;
    border-radius: 50%;
    background-color: #0071e3;
    color: white;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.3s ease;
    box-shadow: 0 2px 8px rgba(0, 113, 227, 0.3);
}

.chat-input button:hover {
    background-color: #0077ed;
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(0, 113, 227, 0.4);
}

.chat-input button:active {
    transform: translateY(1px);
    box-shadow: 0 1px 4px rgba(0, 113, 227, 0.3);
}

/* 右侧文档和日志区域 */
.document-container {
    flex: 1;
    display: flex;
    flex-direction: column;
    background-color: #fff;
}

.document-header {
    padding: 20px;
    border-bottom: 1px solid #d2d2d7;
    background-color: rgba(255, 255, 255, 0.8);
    backdrop-filter: blur(20px);
    -webkit-backdrop-filter: blur(20px);
    position: sticky;
    top: 0;
    z-index: 10;
}

.document-header h2 {
    font-size: 18px; /* 从20px减小到18px */
    font-weight: 600;
    color: #1d1d1f;
    letter-spacing: -0.02em;
}

.document-content {
    flex: 1;
    overflow-y: auto;
    padding: 30px;
    background-color: #fff;
}

.document-content h1 {
    font-size: 32px;
    font-weight: 700;
    margin-bottom: 20px;
    color: #1d1d1f;
    letter-spacing: -0.02em;
}

.document-content h2 {
    font-size: 22px; /* 从24px减小到22px */
    font-weight: 600;
    margin: 30px 0 15px;
    color: #1d1d1f;
    letter-spacing: -0.02em;
}

.document-content p {
    font-size: 14px; /* 从16px减小到14px */
    line-height: 1.6;
    color: #1d1d1f;
    margin-bottom: 15px;
}

.document-content ul {
    margin: 15px 0;
    padding-left: 20px;
}

.document-content li {
    margin-bottom: 10px;
    line-height: 1.6;
    font-size: 14px; /* 添加列表项的字体大小 */
}

.placeholder {
    height: 100%;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    color: #86868b;
    padding: 40px;
}

.placeholder i {
    font-size: 64px;
    margin-bottom: 20px;
    color: #a1a1a6;
}

.placeholder p {
    font-size: 17px;
    text-align: center;
}

.log-container {
    height: 180px;
    border-top: 1px solid #d2d2d7;
    display: flex;
    flex-direction: column;
}

.log-header {
    padding: 12px 20px;
    background-color: rgba(245, 245, 247, 0.8);
    backdrop-filter: blur(20px);
    -webkit-backdrop-filter: blur(20px);
    border-bottom: 1px solid #d2d2d7;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.log-header h3 {
    font-size: 14px; /* 从15px减小到14px */
    font-weight: 600;
    color: #1d1d1f;
    letter-spacing: -0.01em;
}

.log-header button {
    background: none;
    border: none;
    color: #0071e3;
    cursor: pointer;
    font-size: 14px;
    transition: all 0.2s ease;
    padding: 5px 10px;
    border-radius: 4px;
}

.log-header button:hover {
    background-color: rgba(0, 113, 227, 0.1);
}

.log-content {
    flex: 1;
    overflow-y: auto;
    padding: 12px 20px;
    font-family: 'SF Mono', 'Menlo', monospace;
    font-size: 11px; /* 从12px减小到11px */
    background-color: #f5f5f7;
    color: #424245;
}

.log-entry {
    margin-bottom: 6px;
    padding: 4px 0;
    border-bottom: 1px solid #e5e5e5;
    line-height: 1.5;
}

.log-entry.user {
    color: #0071e3;
}

.log-entry.success {
    color: #29cc41;
}

.log-entry.error {
    color: #ff3b30;
}

.log-entry.system {
    color: #9d9d9f;
}

/* 响应式设计 */
@media (max-width: 992px) {
    .container {
        flex-direction: column;
    }
    
    .chat-container {
        width: 100%;
        height: 50%;
    }
    
    .document-container {
        height: 50%;
    }
}

/* 自定义滚动条 */
::-webkit-scrollbar {
    width: 8px;
    height: 8px;
}

::-webkit-scrollbar-track {
    background: #f5f5f7;
}

::-webkit-scrollbar-thumb {
    background: #c1c1c6;
    border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
    background: #a1a1a6;
}

/* 加载动画 */
@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

.fa-spinner {
    animation: spin 1.5s linear infinite;
}