* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Arial', sans-serif;
    line-height: 1.6;
    color: #333;
}

header {
    background-color: #2c3e50;
    padding: 1rem 0;
}

nav ul {
    display: flex;
    list-style: none;
    justify-content: center;
}

nav ul li {
    margin: 0 1rem;
}

nav ul li a {
    color: white;
    text-decoration: none;
}

main {
    max-width: 1200px;
    margin: 0 auto;
    padding: 2rem;
}

.hero {
    text-align: center;
    margin-bottom: 3rem;
}

.hero h1 {
    font-size: 2.5rem;
    margin-bottom: 1rem;
}

.features {
    display: flex;
    justify-content: space-between;
    flex-wrap: wrap;
}

.feature {
    flex: 1;
    min-width: 300px;
    margin: 1rem;
    padding: 1.5rem;
    border-radius: 5px;
    box-shadow: 0 2px 5px rgba(0,0,0,0.1);
}

.about {
    max-width: 800px;
    margin: 0 auto;
}

.about h1, .about h2 {
    margin-bottom: 1rem;
}

.about p {
    margin-bottom: 2rem;
}

footer {
    text-align: center;
    padding: 2rem 0;
    background-color: #f8f9fa;
}