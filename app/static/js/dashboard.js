document.addEventListener('DOMContentLoaded', function() {
    // 获取DOM元素
    const chatMessages = document.getElementById('chat-messages');
    const userInput = document.getElementById('user-input');
    const sendBtn = document.getElementById('send-btn');
    const documentContent = document.getElementById('document-content');
    const logContent = document.getElementById('log-content');
    const clearLogBtn = document.getElementById('clear-log-btn');
    
    // 添加日志函数
    function addLog(message, type = 'info') {
        const logEntry = document.createElement('div');
        logEntry.className = `log-entry ${type}`;
        
        const timestamp = new Date().toLocaleTimeString();
        logEntry.textContent = `[${timestamp}] ${message}`;
        
        logContent.appendChild(logEntry);
        logContent.scrollTop = logContent.scrollHeight;
    }
    
    // 添加聊天消息函数
    function addChatMessage(message, isUser = false) {
        const messageDiv = document.createElement('div');
        messageDiv.className = `message ${isUser ? 'user' : 'system'}`;
        messageDiv.style.opacity = '0';
        messageDiv.style.transform = 'translateY(20px)';
        
        const messageContent = document.createElement('div');
        messageContent.className = 'message-content';
        
        const messagePara = document.createElement('p');
        messagePara.textContent = message;
        
        messageContent.appendChild(messagePara);
        messageDiv.appendChild(messageContent);
        chatMessages.appendChild(messageDiv);
        
        // 添加动画效果
        setTimeout(() => {
            messageDiv.style.transition = 'all 0.3s ease';
            messageDiv.style.opacity = '1';
            messageDiv.style.transform = 'translateY(0)';
        }, 10);
        
        // 滚动到底部
        chatMessages.scrollTop = chatMessages.scrollHeight;
    }
    
    // 发送消息
    function sendMessage() {
        const message = userInput.value.trim();
        if (!message) return;
        
        // 添加用户消息到聊天区域
        addChatMessage(message, true);
        
        // 记录到日志
        addLog(`用户发送消息: ${message}`, 'user');
        
        // 清空输入框
        userInput.value = '';
        
        // 禁用发送按钮，显示加载状态
        sendBtn.disabled = true;
        sendBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i>';
        
        // 模拟API调用
        setTimeout(() => {
            // 恢复发送按钮
            sendBtn.disabled = false;
            sendBtn.innerHTML = '<i class="fas fa-paper-plane"></i>';
            
            // 处理用户消息
            processUserMessage(message);
        }, 500);
    }
    
    // 处理用户消息
    function processUserMessage(message) {
        // 模拟系统响应
        addLog('处理用户消息...', 'system');
        
        // 检查是否是生成月报的命令
        if (message.toLowerCase().includes('生成月报')) {
            addLog('用户请求生成月报', 'info');
            // 提示用户输入模板链接
            setTimeout(() => {
                addChatMessage('请输入本月的月报模板链接用于展示');
                addLog('等待用户输入模板链接', 'system');
                
                // 设置一个标志，表示系统正在等待用户输入链接
                window.waitingForLink = true;
            }, 1000);
            return;
        }
        
        // 如果系统正在等待链接，则将任何输入视为链接
        if (window.waitingForLink) {
            addLog('接收到用户提供的链接', 'info');
            loadDocument(message);
            window.waitingForLink = false;
            return;
        }
        
        // 检查是否是链接
        if (message.toLowerCase().includes('http') || message.toLowerCase().includes('www')) {
            addLog('检测到用户输入链接', 'info');
            loadDocument(message);
            return;
        }
        
        // 检查是否是加载文档的命令
        if (message.toLowerCase().includes('加载文档') || 
            message.toLowerCase().includes('打开文档') || 
            message.toLowerCase().includes('显示文档')) {
            loadDocument(message);
            return;
        }
        
        // 简单的响应逻辑
        let response;
        if (message.toLowerCase().includes('你好') || message.toLowerCase().includes('hello')) {
            response = '您好！我是您的经分月报助手。我可以帮您分析文档并生成报告。';
        } else if (message.toLowerCase().includes('报告') || message.toLowerCase().includes('生成')) {
            response = '要生成报告，请输入"生成月报"来启动任务。';
        } else if (message.toLowerCase().includes('帮助') || message.toLowerCase().includes('help')) {
            response = '我可以帮您：\n1. 生成经分月报\n2. 分析文档内容\n3. 回答关于报告的问题\n请输入"生成月报"开始使用。';
        } else {
            response = '我理解您的问题是关于"' + message + '"。请提供更多细节，我会尽力帮助您。';
        }
        
        // 添加系统响应，模拟打字效果
        const typingIndicator = document.createElement('div');
        typingIndicator.className = 'message system';
        typingIndicator.innerHTML = '<div class="message-content"><p>正在输入...</p></div>';
        chatMessages.appendChild(typingIndicator);
        
        setTimeout(() => {
            // 移除打字指示器
            chatMessages.removeChild(typingIndicator);
            
            // 添加实际响应
            addChatMessage(response);
            addLog('系统响应已发送', 'success');
        }, 1000);
    }
    
    // 加载文档
    function loadDocument(message) {
        addLog('正在解析文档链接...', 'info');
        // 判断是否为特殊指令，打开静态月报页面
        if (
        message.trim() === '/feishu_preview' ||
        message.trim() === 'http://127.0.0.1:5000/feishu_preview'
        ) {
        documentContent.innerHTML = `
        <iframe src="/feishu_preview" style="width:100%;height:90vh;border:none;border-radius:8px;" loading="lazy"></iframe>
        `;
        addLog('已打开静态月报页面', 'success');
        return;
        }
        
        // 显示加载中，添加优雅的加载动画
        documentContent.innerHTML = `
            <div class="placeholder">
                <i class="fas fa-spinner fa-spin"></i>
                <p>正在加载文档，请稍候...</p>
            </div>
        `;
        
        // 提取URL（简单实现，实际应用中可能需要更复杂的正则表达式）
        let url = message;
        
        // 记录尝试加载的链接
        addLog(`尝试加载链接: ${url}`, 'info');
        
        // 模拟文档加载 - 确保这个函数总是被调用
        setTimeout(() => {
            // 这里应该是实际的文档加载逻辑
            fetchDocumentContent(url);
        }, 1500);
    }
    
    // 获取文档内容 - 确保这个函数总是生成内容
    function fetchDocumentContent(url) {
        // 记录API请求
        addLog('向后端发送文档请求...', 'system');
        
        // 自动补全 http 前缀
        if (!/^https?:\/\//i.test(url)) {
            url = 'http://' + url;
        }
    
        // 检查是否是飞书文档链接
        const isFeishuDoc = url.includes('feishu.cn') || url.includes('feishu.com') || url.includes('larksuite.com');
        
        // 添加调试日志
        addLog(`处理链接: ${url}, 是否飞书文档: ${isFeishuDoc}`, 'info');
    
        // 发送请求到后端API
        fetch('/api/feishu/document', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({ 
                url: url,
                is_feishu: isFeishuDoc 
            })
        })
        .then(response => {
            addLog(`API响应状态: ${response.status}`, 'info');
            if (!response.ok) {
                return response.json().then(errData => {
                    throw new Error(errData.error || `获取文档失败: ${response.status}`);
                }).catch(e => {
                    throw new Error(`获取文档失败: ${response.status}`);
                });
            }
            return response.json();
        })
        .then(data => {
            addLog(`成功获取文档数据，类型: ${data.type}`, 'success');
            displayDocumentContent(data);
        })
        .catch(error => {
            addLog(`捕获到错误: ${error.message}`, 'error');
            handleDocumentError(error);
        });
    }
    
    // 处理文档错误
    function handleDocumentError(error) {
        console.error('获取文档失败:', error);
        
        // 显示错误信息
        documentContent.innerHTML = `
            <div class="placeholder">
                <i class="fas fa-exclamation-triangle"></i>
                <p>获取文档失败: ${error.message}</p>
                <p>请检查以下可能的原因:</p>
                <ul style="text-align: left; margin-top: 10px;">
                    <li>网络连接是否正常</li>
                    <li>链接格式是否正确</li>
                    ${error.message.includes('飞书') ? '<li>飞书文档权限是否已设置为"任何人可查看"</li>' : ''}
                    <li>后端服务是否正常运行</li>
                </ul>
                <button class="btn btn-primary mt-3" onclick="location.reload()">刷新页面</button>
            </div>
        `;
        
        addLog(`获取文档失败: ${error.message}`, 'error');
        
        // 通知聊天助手
        addChatMessage('抱歉，获取文档时出现错误。请检查链接是否有效，或确保飞书文档已设置适当的访问权限。');
    }
    
    // 显示文档内容
    function displayDocumentContent(data) {
        documentContent.style.opacity = '0';
    
        if (data.type === "html") {
            // 显示HTML内容
            documentContent.innerHTML = data.content;
            setTimeout(() => {
                documentContent.style.transition = 'opacity 0.5s ease';
                documentContent.style.opacity = '1';
            }, 50);
            addChatMessage('我已经加载了您提供的飞书文档。您可以继续提问或要求我基于此文档生成新的月报。');
        } else if (data.type === "iframe") {
            const iframeHeight = documentContent.clientHeight - 20;
            const iframe = document.createElement('iframe');
            iframe.src = data.url;
            iframe.style.width = '100%';
            iframe.style.height = iframeHeight + 'px';
            iframe.style.border = 'none';
            iframe.style.borderRadius = '8px';
            iframe.setAttribute('sandbox', 'allow-same-origin allow-scripts allow-popups allow-forms');
            iframe.setAttribute('loading', 'lazy');
    
            // 加载失败处理
            let loaded = false;
            iframe.onload = function() {
                loaded = true;
                setTimeout(() => {
                    documentContent.style.transition = 'opacity 0.5s ease';
                    documentContent.style.opacity = '1';
                }, 50);
            };
            setTimeout(() => {
                if (!loaded) {
                    documentContent.innerHTML = `
                        <div class="placeholder">
                            <i class="fas fa-exclamation-circle"></i>
                            <p>该网页禁止嵌入预览，请 <a href="${data.url}" target="_blank" style="color:#0071e3;">点击这里</a> 在新窗口打开。</p>
                        </div>
                    `;
                    documentContent.style.opacity = '1';
                }
            }, 2000);
    
            documentContent.innerHTML = '';
            documentContent.appendChild(iframe);
        } else if (data.content) {
            documentContent.innerHTML = data.content;
            setTimeout(() => {
                documentContent.style.transition = 'opacity 0.5s ease';
                documentContent.style.opacity = '1';
            }, 50);
        } else {
            documentContent.innerHTML = `
                <div class="placeholder">
                    <i class="fas fa-exclamation-circle"></i>
                    <p>无法获取文档内容，请检查链接是否有效</p>
                </div>
            `;
            documentContent.style.opacity = '1';
        }
    
        addLog('文档加载完成', 'success');
    }
    
    // 清空日志
    function clearLog() {
        // 添加清空动画
        logContent.style.transition = 'opacity 0.3s ease';
        logContent.style.opacity = '0';
        
        setTimeout(() => {
            logContent.innerHTML = '';
            addLog('日志已清空');
            logContent.style.opacity = '1';
        }, 300);
    }
    
    // 事件监听
    sendBtn.addEventListener('click', sendMessage);
    
    userInput.addEventListener('keypress', function(e) {
        if (e.key === 'Enter' && !e.shiftKey) {
            e.preventDefault();
            sendMessage();
        }
    });
    
    // 输入框获取焦点时的效果
    userInput.addEventListener('focus', function() {
        this.parentElement.style.boxShadow = '0 0 0 2px rgba(0, 113, 227, 0.2)';
    });
    
    userInput.addEventListener('blur', function() {
        this.parentElement.style.boxShadow = 'none';
    });
    
    clearLogBtn.addEventListener('click', clearLog);
    
    // 初始化
    addLog('系统初始化完成');
    window.waitingForLink = false; // 初始化标志
    
    // 添加欢迎消息，带有延迟效果
    setTimeout(() => {
        addChatMessage('欢迎使用经分月报自动化生成系统。请输入"生成月报"来启动任务，或输入"帮助"获取更多信息。');
    }, 500);
});


// 新增：飞书扫码弹窗
function showFeishuScanDialog() {
    // 你可以将二维码图片地址替换为你后端生成的飞书 OAuth2 二维码链接
    const scanHtml = `
        <div class="placeholder">
            <i class="fas fa-qrcode" style="font-size:48px;color:#0071e3;"></i>
            <p>飞书文档需要授权访问，请扫码登录飞书后重试。</p>
            <img src="https://open.feishu.cn/open-apis/authen/v1/qrcode?app_id=YOUR_APP_ID" alt="飞书扫码登录" style="width:180px;height:180px;margin:16px 0;">
            <p style="font-size:13px;color:#888;">扫码后请刷新页面或重新提交文档链接。</p>
        </div>
    `;
    documentContent.innerHTML = scanHtml;
    addLog('飞书文档需要扫码授权', 'warning');
    addChatMessage('飞书文档需要扫码授权，请用飞书App扫码后重试。');
}