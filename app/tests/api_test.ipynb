{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# 用户注册"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["状态码: {\n", "  \"code\": \"0\", \n", "  \"data\": {\n", "    \"result\": {\n", "      \"code\": 0, \n", "      \"document_id\": \"CyBydegl7oLBKmxZXwdc2XXnnth\", \n", "      \"document_url\": \"https://bytedance.larkoffice.com/docx/CyBydegl7oLBKmxZXwdc2XXnnth\", \n", "      \"msg\": \"\\u6587\\u6863\\u751f\\u6210\\u6210\\u529f\"\n", "    }\n", "  }, \n", "  \"msg\": \"success\"\n", "}\n", "\n"]}], "source": ["import requests\n", "import json\n", "###创建月报模版示例\n", "url='http://127.0.0.1:5000/api/report_etl/create_doc_template'\n", "headers = {\"Content-Type\": \"application/json\"}\n", "response = requests.post(url, headers=headers,json={\"open_id\":\"ou_7e37b73cda3c4b2ef568e85d015eb06b\"})\n", "\n", "# 打印响应结果\n", "print(f\"状态码: {response.text}\")"]}, {"cell_type": "code", "execution_count": 17, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["{'code': '0', 'data': {'result': {'code': 0, 'data': [{'_id': 1830175372311591, '_isDeleted': False, '_objectId': 1817407893780627, 'block_id': 'doxcnKIe5gzpKM9nGc9WaAVXuR2', 'document_id': 'IUqodQwmAok1odxeX8Ecx7Atn9e', 'report_name': 'X月量级变动详细情况', 'sheet_id': 'AfGBcX', 'spreadsheet_token': 'EKcFs7tSOhriAKtI6vYcTASHnjh'}, {'_id': 1830175372313639, '_isDeleted': False, '_objectId': 1817407893780627, 'block_id': 'doxcnh6FrpAqo6xPrLPOll06ote', 'document_id': 'IUqodQwmAok1odxeX8Ecx7Atn9e', 'report_name': 'X月关键效率变动情况', 'sheet_id': 'ikFg4k', 'spreadsheet_token': 'EKcFs7tSOhriAKtI6vYcTASHnjh'}, {'_id': 1830176519526579, '_isDeleted': False, '_objectId': 1817407893780627, 'block_id': 'doxcnyIPnWMG25Uv6zlNAoA1Rcp', 'document_id': 'IUqodQwmAok1odxeX8Ecx7Atn9e', 'report_name': 'X月人力vs12月变动情况', 'sheet_id': 'uOf1wz', 'spreadsheet_token': 'EKcFs7tSOhriAKtI6vYcTASHnjh'}, {'_id': 1830178000035875, '_isDeleted': False, '_objectId': 1817407893780627, 'block_id': 'doxcnhPAs4aQRwjTSngf1lXDmQb', 'document_id': 'IUqodQwmAok1odxeX8Ecx7Atn9e', 'report_name': '2月人力vs规划变动情况【阅评】', 'sheet_id': 'n83h91', 'spreadsheet_token': 'EKcFs7tSOhriAKtI6vYcTASHnjh'}], 'msg': '查询成功！'}}, 'msg': 'success'}\n"]}], "source": ["###获取文档block信息示例\n", "url='http://127.0.0.1:5001/api/report_etl/get_sheet_block'\n", "headers = {\"Content-Type\": \"application/json\"}\n", "payload = {\n", "    \"document_id\": \"IUqodQwmAok1odxeX8Ecx7Atn9e\",\n", "}\n", "response = requests.post(url, headers=headers,json=payload)\n", "\n", "# 打印响应结果\n", "print(response.json())"]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["{'data': {'sendId': '425445b51b314c31bd71fbc2c1b728b5'}, 'logId': '20250428195055E5CD21AC7B6A5E37699F', 'message': '操作成功!', 'status': '200', 'success': True, 'timestamp': 1745841055890}\n"]}], "source": ["####数据解读触发\n", "url='http://127.0.0.1:5001/api/report_etl/trigger_data_analysis'\n", "headers = {\"Content-Type\": \"application/json\"}\n", "payload = {\n", "    \"document_id\": \"CyBydegl7oLBKmxZXwdc2XXnnth\",\n", "}\n", "response = requests.post(url, headers=headers,json=payload)\n", "\n", "# 打印响应结果\n", "print(response.json())"]}, {"cell_type": "markdown", "metadata": {}, "source": []}, {"cell_type": "code", "execution_count": 18, "metadata": {}, "outputs": [], "source": ["block_info=response.json()['data']\n", "blocks=block_info['result']['data']"]}, {"cell_type": "code", "execution_count": 19, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["{'code': '0', 'data': {'_flowExecutionID': 1830184208044123}, 'message': [{'language_code': 1033, 'text': 'Success'}, {'language_code': 2052, 'text': '成功'}], 'status': 'success', 'success': True}\n", "{'code': '0', 'data': {'_flowExecutionID': 1830184214467651}, 'message': [{'language_code': 1033, 'text': 'Success'}, {'language_code': 2052, 'text': '成功'}], 'status': 'success', 'success': True}\n", "{'code': '0', 'data': {'_flowExecutionID': 1830184209765658}, 'message': [{'language_code': 1033, 'text': 'Success'}, {'language_code': 2052, 'text': '成功'}], 'status': 'success', 'success': True}\n", "{'code': '0', 'data': {'_flowExecutionID': 1830184217053187}, 'message': [{'language_code': 1033, 'text': 'Success'}, {'language_code': 2052, 'text': '成功'}], 'status': 'success', 'success': True}\n"]}], "source": ["###数据写入示例\n", "for item in blocks:\n", "    url='http://127.0.0.1:5001/api/report_etl/trigger_batch_insert'\n", "    headers = {\"Content-Type\": \"application/json\"}\n", "    payload = {\n", "        \"spreadsheet_token\": item['spreadsheet_token'],\n", "        \"sheet_id\": item['sheet_id']\n", "    }\n", "    response = requests.post(url, headers=headers,json=payload)\n", "\n", "    # 打印响应结果\n", "    print(response.json())"]}, {"cell_type": "code", "execution_count": 17, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["状态码: 200\n", "响应内容: {\n", "  \"message\": \"用户注册成功\",\n", "  \"success\": true,\n", "  \"user_id\": \"test_user_002\"\n", "}\n"]}], "source": ["import requests\n", "import json\n", "\n", "# 正确的接口URL\n", "url = \"http://127.0.0.1:5000/api/register\"\n", "# url = \"http://cqctool-cbao.bytedance.net/api/register\"\n", "# 请求数据\n", "payload = {\n", "    \"user_id\": \"test_user_002\",\n", "    \"user_token\": \"u-hm9J7GlxB84H1cw9wCgtbC51nHxQ508pii00hlyyEew7\"\n", "}\n", "\n", "# 发送POST请求\n", "headers = {\"Content-Type\": \"application/json\"}\n", "response = requests.post(url, json=payload, headers=headers)\n", "\n", "# 打印响应结果\n", "print(f\"状态码: {response.status_code}\")\n", "try:\n", "    print(f\"响应内容: {json.dumps(response.json(), ensure_ascii=False, indent=2)}\")\n", "except:\n", "    print(f\"响应内容: {response.text}\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# 获取月报模板中的表格block"]}, {"cell_type": "code", "execution_count": 18, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["500\n", "{'error': \"读取飞书文档失败: cannot access local variable 'block_info' where it is not associated with a value\"}\n"]}, {"ename": "KeyError", "evalue": "'block_id'", "output_type": "error", "traceback": ["\u001b[0;31m---------------------------------------------------------------------------\u001b[0m", "\u001b[0;31m<PERSON><PERSON><PERSON><PERSON><PERSON>\u001b[0m                                  <PERSON><PERSON> (most recent call last)", "Cell \u001b[0;32mIn[18], line 16\u001b[0m\n\u001b[1;32m     14\u001b[0m \u001b[38;5;28mprint\u001b[39m(response\u001b[38;5;241m.\u001b[39mstatus_code)\n\u001b[1;32m     15\u001b[0m \u001b[38;5;28mprint\u001b[39m(response\u001b[38;5;241m.\u001b[39mjson())\n\u001b[0;32m---> 16\u001b[0m \u001b[38;5;28;01mif\u001b[39;00m \u001b[38;5;129;01mnot\u001b[39;00m response\u001b[38;5;241m.\u001b[39mjson()[\u001b[38;5;124m'\u001b[39m\u001b[38;5;124mblock_id\u001b[39m\u001b[38;5;124m'\u001b[39m]:\u001b[38;5;28;01<PERSON><PERSON>\u001b[39;00m\n", "\u001b[0;31mKeyError\u001b[0m: 'block_id'"]}], "source": ["while True:\n", "    import requests\n", "\n", "    # url = \"http://cqctool-cbao.bytedance.net/api/feishu/get_sheet_block\"\n", "    url = \"http://127.0.0.1:5000/api/feishu/get_sheet_block\"\n", "    payload = {\n", "        \"user_id\":\"test_user_002\",\n", "        \"url\": \"https://bytedance.larkoffice.com/docx/E6Qzd7jQUoKusnxNBUXcjiBHnlc\",  # 替换为实际飞书文档链接\n", "        \"user_token\": \"**********************************************\"  # 如无需用户token可去掉此行或设为None\n", "    }\n", "    headers = {\"Content-Type\": \"application/json\"}\n", "\n", "    response = requests.post(url, json=payload, headers=headers)\n", "    print(response.status_code)\n", "    print(response.json())\n", "    if not response.json()['block_id']:break "]}, {"cell_type": "markdown", "metadata": {}, "source": ["# 群组月报检查"]}, {"cell_type": "code", "execution_count": 5, "metadata": {}, "outputs": [], "source": ["a={'code': 0, 'data': {'revision': 106, 'spreadsheetToken': 'XwhYsS6Ishqt2ytqUTjcWlJgnog', 'valueRange': {'majorDimension': 'ROWS', 'range': 'ZbeDXh!A1:B10', 'revision': 106, 'values': [['群组', '月报链接'], ['垂直群组', [{'text': ' ', 'type': 'text'}, {'link': 'https://bytedance.larkoffice.com/docx/D3bldrWcCoe2ELxx4zccG4nBnje', 'mentionNotify': False, 'mentionType': 22, 'text': '经营3月报「垂直群组」（初稿）', 'token': 'D3bldrWcCoe2ELxx4zccG4nBnje', 'type': 'mention'}, {'text': ' ', 'type': 'text'}]], ['头条群组', [{'link': 'https://bytedance.larkoffice.com/docx/LqdXdW87voPPkFx0LMtcIsGXnJi', 'mentionNotify': False, 'mentionType': 22, 'text': '头条群组3月月报', 'token': 'LqdXdW87voPPkFx0LMtcIsGXnJi', 'type': 'mention'}, {'text': ' ', 'type': 'text'}]], ['抖音群组', [{'link': 'https://bytedance.larkoffice.com/docx/PQJhdDQrkopd1nxCUXxcSqtpn0e', 'mentionNotify': False, 'mentionType': 22, 'text': '抖音群组月报—2025.03', 'token': 'PQJhdDQrkopd1nxCUXxcSqtpn0e', 'type': 'mention'}, {'text': ' ', 'type': 'text'}]], ['直播群组', [{'link': 'https://bytedance.larkoffice.com/docx/QLKTdLZLYodWcGxXkZ9cOH5rnfe', 'mentionNotify': False, 'mentionType': 22, 'text': 'CQC目标追踪 — 直播群组月报（2025年3月）', 'token': 'QLKTdLZLYodWcGxXkZ9cOH5rnfe', 'type': 'mention'}, {'text': ' ', 'type': 'text'}]], ['商安-广告经营', [{'link': 'https://bytedance.larkoffice.com/docx/XQHPdtJz5o37MmxGaWIcnJQ4nMg', 'mentionNotify': False, 'mentionType': 22, 'text': '【广告群组】经营月报-3月', 'token': 'XQHPdtJz5o37MmxGaWIcnJQ4nMg', 'type': 'mention'}, {'text': ' ', 'type': 'text'}]], ['商安-生活服务', [{'link': 'https://bytedance.larkoffice.com/docx/GMu1dgVKWoFYomxl4O0cNdX7nnk', 'mentionNotify': False, 'mentionType': 22, 'text': 'CQC目标追踪 — 生服群组月报（25年3月）', 'token': 'GMu1dgVKWoFYomxl4O0cNdX7nnk', 'type': 'mention'}, {'text': ' ', 'type': 'text'}]], ['商安-电商', [{'link': 'https://bytedance.larkoffice.com/docx/KgKXdk5K0owxlPxcec4cktCkn9g', 'mentionNotify': False, 'mentionType': 22, 'text': ' 电商群组月报（含Q2规划，DDL 4月7日） ', 'token': 'KgKXdk5K0owxlPxcec4cktCkn9g', 'type': 'mention'}, {'text': ' ', 'type': 'text'}]], ['运营支持中心', [{'link': 'https://bytedance.larkoffice.com/docx/TzIbdvZJ4oBmyKxNf6ccRQ9enAb', 'mentionNotify': False, 'mentionType': 22, 'text': '运营支持中心月报-3月', 'token': 'TzIbdvZJ4oBmyKxNf6ccRQ9enAb', 'type': 'mention'}, {'text': ' ', 'type': 'text'}]], ['DMC', [{'link': 'https://bytedance.larkoffice.com/docx/ASzGdv9f9oOZxWxmQ2dckvTvnEd', 'mentionNotify': False, 'mentionType': 22, 'text': '季度会中涉及DMC数据结果对齐', 'token': 'ASzGdv9f9oOZxWxmQ2dckvTvnEd', 'type': 'mention'}, {'text': ' ', 'type': 'text'}]]]}}, 'msg': 'success'}"]}, {"cell_type": "code", "execution_count": 9, "metadata": {}, "outputs": [{"data": {"text/plain": ["[['群组', '月报链接'],\n", " ['垂直群组',\n", "  [{'text': ' ', 'type': 'text'},\n", "   {'link': 'https://bytedance.larkoffice.com/docx/D3bldrWcCoe2ELxx4zccG4nBnje',\n", "    'mentionNotify': <PERSON><PERSON><PERSON>,\n", "    'mentionType': 22,\n", "    'text': '经营3月报「垂直群组」（初稿）',\n", "    'token': 'D3bldrWcCoe2ELxx4zccG4nBnje',\n", "    'type': 'mention'},\n", "   {'text': ' ', 'type': 'text'}]],\n", " ['头条群组',\n", "  [{'link': 'https://bytedance.larkoffice.com/docx/LqdXdW87voPPkFx0LMtcIsGXnJi',\n", "    'mentionNotify': <PERSON><PERSON><PERSON>,\n", "    'mentionType': 22,\n", "    'text': '头条群组3月月报',\n", "    'token': 'LqdXdW87voPPkFx0LMtcIsGXnJi',\n", "    'type': 'mention'},\n", "   {'text': ' ', 'type': 'text'}]],\n", " ['抖音群组',\n", "  [{'link': 'https://bytedance.larkoffice.com/docx/PQJhdDQrkopd1nxCUXxcSqtpn0e',\n", "    'mentionNotify': <PERSON><PERSON><PERSON>,\n", "    'mentionType': 22,\n", "    'text': '抖音群组月报—2025.03',\n", "    'token': 'PQJhdDQrkopd1nxCUXxcSqtpn0e',\n", "    'type': 'mention'},\n", "   {'text': ' ', 'type': 'text'}]],\n", " ['直播群组',\n", "  [{'link': 'https://bytedance.larkoffice.com/docx/QLKTdLZLYodWcGxXkZ9cOH5rnfe',\n", "    'mentionNotify': <PERSON><PERSON><PERSON>,\n", "    'mentionType': 22,\n", "    'text': 'CQC目标追踪 — 直播群组月报（2025年3月）',\n", "    'token': 'QLKTdLZLYodWcGxXkZ9cOH5rnfe',\n", "    'type': 'mention'},\n", "   {'text': ' ', 'type': 'text'}]],\n", " ['商安-广告经营',\n", "  [{'link': 'https://bytedance.larkoffice.com/docx/XQHPdtJz5o37MmxGaWIcnJQ4nMg',\n", "    'mentionNotify': <PERSON><PERSON><PERSON>,\n", "    'mentionType': 22,\n", "    'text': '【广告群组】经营月报-3月',\n", "    'token': 'XQHPdtJz5o37MmxGaWIcnJQ4nMg',\n", "    'type': 'mention'},\n", "   {'text': ' ', 'type': 'text'}]],\n", " ['商安-生活服务',\n", "  [{'link': 'https://bytedance.larkoffice.com/docx/GMu1dgVKWoFYomxl4O0cNdX7nnk',\n", "    'mentionNotify': <PERSON><PERSON><PERSON>,\n", "    'mentionType': 22,\n", "    'text': 'CQC目标追踪 — 生服群组月报（25年3月）',\n", "    'token': 'GMu1dgVKWoFYomxl4O0cNdX7nnk',\n", "    'type': 'mention'},\n", "   {'text': ' ', 'type': 'text'}]],\n", " ['商安-电商',\n", "  [{'link': 'https://bytedance.larkoffice.com/docx/KgKXdk5K0owxlPxcec4cktCkn9g',\n", "    'mentionNotify': <PERSON><PERSON><PERSON>,\n", "    'mentionType': 22,\n", "    'text': ' 电商群组月报（含Q2规划，DDL 4月7日） ',\n", "    'token': 'KgKXdk5K0owxlPxcec4cktCkn9g',\n", "    'type': 'mention'},\n", "   {'text': ' ', 'type': 'text'}]],\n", " ['运营支持中心',\n", "  [{'link': 'https://bytedance.larkoffice.com/docx/TzIbdvZJ4oBmyKxNf6ccRQ9enAb',\n", "    'mentionNotify': <PERSON><PERSON><PERSON>,\n", "    'mentionType': 22,\n", "    'text': '运营支持中心月报-3月',\n", "    'token': 'TzIbdvZJ4oBmyKxNf6ccRQ9enAb',\n", "    'type': 'mention'},\n", "   {'text': ' ', 'type': 'text'}]],\n", " ['DMC',\n", "  [{'link': 'https://bytedance.larkoffice.com/docx/ASzGdv9f9oOZxWxmQ2dckvTvnEd',\n", "    'mentionNotify': <PERSON><PERSON><PERSON>,\n", "    'mentionType': 22,\n", "    'text': '季度会中涉及DMC数据结果对齐',\n", "    'token': 'ASzGdv9f9oOZxWxmQ2dckvTvnEd',\n", "    'type': 'mention'},\n", "   {'text': ' ', 'type': 'text'}]]]"]}, "execution_count": 9, "metadata": {}, "output_type": "execute_result"}], "source": ["a['data']['valueRange']['values']"]}, {"cell_type": "code", "execution_count": 19, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["200\n", "{'group_report_status': True, 'message': '完成群组月报配置检查', 'user_id': 'test_user_002'}\n"]}], "source": ["import requests\n", "\n", "# url = \"http://cqctool-cbao.bytedance.net/api/feishu/group_report_check\"\n", "url = \"http://127.0.0.1:5000/api/feishu/group_report_check\"\n", "payload = {\n", "    \"user_id\":\"test_user_002\"\n", "}\n", "headers = {\"Content-Type\": \"application/json\"}\n", "\n", "response = requests.post(url, json=payload, headers=headers)\n", "print(response.status_code)\n", "print(response.json())"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# RAG初始化"]}, {"cell_type": "code", "execution_count": 20, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["500\n", "{'message': \"初始化失败: 清理RAG文档失败: HTTPConnectionPool(host='localhost', port=9621): Max retries exceeded with url: /documents (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x1269a8b60>: Failed to establish a new connection: [Errno 61] Connection refused'))\", 'success': False}\n"]}], "source": ["import requests\n", "\n", "# url = \"http://cqctool-cbao.bytedance.net/api/rag_init\"\n", "url = \"http://127.0.0.1:5000/api/rag_init\"\n", "\n", "payload = {\n", "    \"user_id\":\"test_user_002\",\n", "    \"user_token\":\"u-hm9J7GlxB84H1cw9wCgtbC51nHxQ508pii00hlyyEew7\"\n", "}\n", "headers = {\"Content-Type\": \"application/json\"}\n", "\n", "response = requests.post(url, json=payload, headers=headers,timeout=300)\n", "print(response.status_code)\n", "print(response.json())"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# rag2sheet"]}, {"cell_type": "code", "execution_count": 82, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["504\n"]}, {"ename": "JSONDecodeError", "evalue": "Expecting value: line 1 column 1 (char 0)", "output_type": "error", "traceback": ["\u001b[0;31m---------------------------------------------------------------------------\u001b[0m", "\u001b[0;31mJSONDecodeError\u001b[0m                           Traceback (most recent call last)", "File \u001b[0;32m~/miniconda3/lib/python3.12/site-packages/requests/models.py:974\u001b[0m, in \u001b[0;36mResponse.json\u001b[0;34m(self, **kwargs)\u001b[0m\n\u001b[1;32m    973\u001b[0m \u001b[38;5;28;01mtry\u001b[39;00m:\n\u001b[0;32m--> 974\u001b[0m     \u001b[38;5;28;01m<PERSON><PERSON>\u001b[39;00m complex<PERSON>son\u001b[38;5;241m.\u001b[39mloads(\u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39mtext, \u001b[38;5;241m*\u001b[39m\u001b[38;5;241m*\u001b[39mkwargs)\n\u001b[1;32m    975\u001b[0m \u001b[38;5;28;01mexcept\u001b[39;00m JSONDecodeError \u001b[38;5;28;01mas\u001b[39;00m e:\n\u001b[1;32m    976\u001b[0m     \u001b[38;5;66;03m# Catch JSON-related errors and raise as requests.JSONDecodeError\u001b[39;00m\n\u001b[1;32m    977\u001b[0m     \u001b[38;5;66;03m# This aliases json.JSONDecodeError and simplejson.JSONDecodeError\u001b[39;00m\n", "File \u001b[0;32m~/miniconda3/lib/python3.12/json/__init__.py:346\u001b[0m, in \u001b[0;36mloads\u001b[0;34m(s, cls, object_hook, parse_float, parse_int, parse_constant, object_pairs_hook, **kw)\u001b[0m\n\u001b[1;32m    343\u001b[0m \u001b[38;5;28;01mif\u001b[39;00m (\u001b[38;5;28mcls\u001b[39m \u001b[38;5;129;01mis\u001b[39;00m \u001b[38;5;28;01m<PERSON><PERSON>\u001b[39;00m \u001b[38;5;129;01mand\u001b[39;00m object_hook \u001b[38;5;129;01mis\u001b[39;00m \u001b[38;5;28;01m<PERSON><PERSON>\u001b[39;00m \u001b[38;5;129;01mand\u001b[39;00m\n\u001b[1;32m    344\u001b[0m         parse_int \u001b[38;5;129;01mis\u001b[39;00m \u001b[38;5;28;01m<PERSON><PERSON>\u001b[39;00m \u001b[38;5;129;01mand\u001b[39;00m parse_float \u001b[38;5;129;01mis\u001b[39;00m \u001b[38;5;28;01m<PERSON><PERSON>\u001b[39;00m \u001b[38;5;129;01mand\u001b[39;00m\n\u001b[1;32m    345\u001b[0m         parse_constant \u001b[38;5;129;01mis\u001b[39;00m \u001b[38;5;28;01m<PERSON><PERSON>\u001b[39;00m \u001b[38;5;129;01mand\u001b[39;00m object_pairs_hook \u001b[38;5;129;01mis\u001b[39;00m \u001b[38;5;28;01mNone\u001b[39;00m \u001b[38;5;129;01mand\u001b[39;00m \u001b[38;5;129;01mnot\u001b[39;00m kw):\n\u001b[0;32m--> 346\u001b[0m     \u001b[38;5;28;01mreturn\u001b[39;00m _default_decoder\u001b[38;5;241m.\u001b[39mdecode(s)\n\u001b[1;32m    347\u001b[0m \u001b[38;5;28;01mif\u001b[39;00m \u001b[38;5;28mcls\u001b[39m \u001b[38;5;129;01mis\u001b[39;00m \u001b[38;5;28;01mNone\u001b[39;00m:\n", "File \u001b[0;32m~/miniconda3/lib/python3.12/json/decoder.py:337\u001b[0m, in \u001b[0;36mJSONDecoder.decode\u001b[0;34m(self, s, _w)\u001b[0m\n\u001b[1;32m    333\u001b[0m \u001b[38;5;250m\u001b[39m\u001b[38;5;124;03m\"\"\"Return the Python representation of ``s`` (a ``str`` instance\u001b[39;00m\n\u001b[1;32m    334\u001b[0m \u001b[38;5;124;03mcontaining a JSON document).\u001b[39;00m\n\u001b[1;32m    335\u001b[0m \n\u001b[1;32m    336\u001b[0m \u001b[38;5;124;03m\"\"\"\u001b[39;00m\n\u001b[0;32m--> 337\u001b[0m obj, end \u001b[38;5;241m=\u001b[39m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39mraw_decode(s, idx\u001b[38;5;241m=\u001b[39m_w(s, \u001b[38;5;241m0\u001b[39m)\u001b[38;5;241m.\u001b[39mend())\n\u001b[1;32m    338\u001b[0m end \u001b[38;5;241m=\u001b[39m _w(s, end)\u001b[38;5;241m.\u001b[39mend()\n", "File \u001b[0;32m~/miniconda3/lib/python3.12/json/decoder.py:355\u001b[0m, in \u001b[0;36mJSONDecoder.raw_decode\u001b[0;34m(self, s, idx)\u001b[0m\n\u001b[1;32m    354\u001b[0m \u001b[38;5;28;01mexcept\u001b[39;00m \u001b[38;5;167;01mStopIteration\u001b[39;00m \u001b[38;5;28;01mas\u001b[39;00m err:\n\u001b[0;32m--> 355\u001b[0m     \u001b[38;5;28;01mraise\u001b[39;00m JSONDecodeError(\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mExpecting value\u001b[39m\u001b[38;5;124m\"\u001b[39m, s, err\u001b[38;5;241m.\u001b[39mvalue) \u001b[38;5;28;01mfrom\u001b[39;00m \u001b[38;5;28;01m<PERSON><PERSON>\u001b[39;00m\n\u001b[1;32m    356\u001b[0m \u001b[38;5;28;01mreturn\u001b[39;00m obj, end\n", "\u001b[0;31mJSONDecodeError\u001b[0m: Expecting value: line 1 column 1 (char 0)", "\nDuring handling of the above exception, another exception occurred:\n", "\u001b[0;31mJSONDecodeError\u001b[0m                           Traceback (most recent call last)", "Cell \u001b[0;32mIn[82], line 15\u001b[0m\n\u001b[1;32m     13\u001b[0m response \u001b[38;5;241m=\u001b[39m requests\u001b[38;5;241m.\u001b[39mpost(url, json\u001b[38;5;241m=\u001b[39mpayload, headers\u001b[38;5;241m=\u001b[39mheaders)\n\u001b[1;32m     14\u001b[0m \u001b[38;5;28mprint\u001b[39m(response\u001b[38;5;241m.\u001b[39mstatus_code)\n\u001b[0;32m---> 15\u001b[0m \u001b[38;5;28mprint\u001b[39m(response\u001b[38;5;241m.\u001b[39mjson())\n", "File \u001b[0;32m~/miniconda3/lib/python3.12/site-packages/requests/models.py:978\u001b[0m, in \u001b[0;36mResponse.json\u001b[0;34m(self, **kwargs)\u001b[0m\n\u001b[1;32m    974\u001b[0m     \u001b[38;5;28;01<PERSON><PERSON><PERSON>\u001b[39;00m <PERSON><PERSON><PERSON>\u001b[38;5;241m.\u001b[39mloads(\u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39mtext, \u001b[38;5;241m*\u001b[39m\u001b[38;5;241m*\u001b[39mkwargs)\n\u001b[1;32m    975\u001b[0m \u001b[38;5;28;01mexcept\u001b[39;00m JSONDecodeError \u001b[38;5;28;01mas\u001b[39;00m e:\n\u001b[1;32m    976\u001b[0m     \u001b[38;5;66;03m# Catch JSON-related errors and raise as requests.JSONDecodeError\u001b[39;00m\n\u001b[1;32m    977\u001b[0m     \u001b[38;5;66;03m# This aliases json.JSONDecodeError and simplejson.JSONDecodeError\u001b[39;00m\n\u001b[0;32m--> 978\u001b[0m     \u001b[38;5;28;01mraise\u001b[39;00m RequestsJSONDecodeError(e\u001b[38;5;241m.\u001b[39mmsg, e\u001b[38;5;241m.\u001b[39mdoc, e\u001b[38;5;241m.\u001b[39mpos)\n", "\u001b[0;31mJSONDecodeError\u001b[0m: Expecting value: line 1 column 1 (char 0)"]}], "source": ["import requests\n", "\n", "# url = \"http://127.0.0.1:5000/api/rag2sheet\"\n", "url = \"http://cqctool-cbao.bytedance.net/api/rag2sheet\"\n", "\n", "payload = {\n", "    \"user_id\":\"test_user_001\",\n", "    \"sheet_token\":\"SxsNsUWvwhRMILtDq8dcNZKBnoc_n83h91\",\n", "    \"user_token\":\"u-ivoyIPczt6YFt6YXEmsqffk12Jp050yjUMG0h4K02Gbn\"\n", "}\n", "headers = {\"Content-Type\": \"application/json\"}\n", "\n", "response = requests.post(url, json=payload, headers=headers)\n", "print(response.status_code)\n", "print(response.json())"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# 总结改写"]}, {"cell_type": "code", "execution_count": 5, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["200\n", "{'message': [{'block_id': 'W8XYdLK9JoITNRxYBvKcmwHvnRR', 'block_type': 1, 'children': ['doxcnsF3ZGanyxTgFw6WQodS0fg', 'doxcnvTi0gyMofvrPU7kDw9zH8g', 'doxcnwuXCBKrJFsRu4152aJCtWg', 'doxcnLUYBc5SGqU46xAiKFNg5bb', 'doxcnt4YDiuMpQDd9JcY4dziF4f', 'doxcnbgtdVPHiTV0i7McIx0faxe', 'doxcnVCDmM8LoM8igP79VlgYRpb', 'doxcn4tGsFMNCiySxl9GCF7Kymc', 'doxcnYyvLoFOf6Oi5fBfJMPi0bb', 'doxcnpsa9rYQWyd6PjjNRCw8zsd', 'doxcn5hmFVoWktM3V6Fnmhe0JQe', 'doxcnlVFWLckabAoHP8KnBCIKFg', 'doxcnCYw6gDFLQjlnnLH5zSSTEh', 'doxcnkK8ZsI76ie1QuZSgMl6HCf', 'doxcniFITmqYuod8lFal89Q0Dih'], 'page': {'elements': [{'text_run': {'content': '4月月报模板— for 智能周报--开发版本 副本', 'text_element_style': {'bold': False, 'inline_code': False, 'italic': False, 'strikethrough': False, 'underline': False}}}], 'style': {'align': 2}}, 'parent_id': ''}, {'block_id': 'doxcnsF3ZGanyxTgFw6WQodS0fg', 'block_type': 5, 'heading3': {'elements': [{'text_run': {'content': '一、 月度经营总览', 'text_element_style': {'bold': False, 'inline_code': False, 'italic': False, 'strikethrough': False, 'underline': False}}}], 'style': {'align': 1, 'folded': False}}, 'parent_id': 'W8XYdLK9JoITNRxYBvKcmwHvnRR'}, {'block_id': 'doxcnvTi0gyMofvrPU7kDw9zH8g', 'block_type': 19, 'callout': {'background_color': 2, 'border_color': 2, 'emoji_id': 'pushpin'}, 'children': ['doxcnu4RlJ7Su2UUojp66MGIvZg', 'doxcngvY5S8xlYPYpq5ktpnEQKd'], 'parent_id': 'W8XYdLK9JoITNRxYBvKcmwHvnRR'}, {'block_id': 'doxcnu4RlJ7Su2UUojp66MGIvZg', 'block_type': 2, 'parent_id': 'doxcnvTi0gyMofvrPU7kDw9zH8g', 'text': {'elements': [{'text_run': {'content': '测试块', 'text_element_style': {'bold': False, 'inline_code': False, 'italic': False, 'strikethrough': False, 'underline': False}}}], 'style': {'align': 1, 'folded': False}}}, {'block_id': 'doxcngvY5S8xlYPYpq5ktpnEQKd', 'block_type': 12, 'bullet': {'elements': [{'text_run': {'content': '量级变化：25年11111-月', 'text_element_style': {'bold': True, 'inline_code': False, 'italic': False, 'strikethrough': False, 'underline': False}}}], 'style': {'align': 1, 'folded': False}}, 'parent_id': 'doxcnvTi0gyMofvrPU7kDw9zH8g'}, {'block_id': 'doxcnwuXCBKrJFsRu4152aJCtWg', 'block_type': 19, 'callout': {'border_color': 5, 'emoji_id': 'lower_left_crayon'}, 'children': ['doxcnsDkYkQhHdxoDwbvQTwQ58g', 'doxcntyMUUaClIyc9CrUMwXKxUe', 'doxcn7wqrN4LWuurjD5aaMqfnoc', 'doxcnTpL3EVOw3lrEkoaoQ1F7Zb'], 'parent_id': 'W8XYdLK9JoITNRxYBvKcmwHvnRR'}, {'block_id': 'doxcnsDkYkQhHdxoDwbvQTwQ58g', 'block_type': 12, 'bullet': {'elements': [{'text_run': {'content': '总结概述：', 'text_element_style': {'bold': True, 'inline_code': False, 'italic': False, 'strikethrough': False, 'underline': False}}}], 'style': {'align': 1, 'folded': False}}, 'children': ['doxcnbQbemUXAZ2MF8ZJ7Vm4wLf', 'doxcn6GaSGisna1HDZNj6TS4nUb'], 'parent_id': 'doxcnwuXCBKrJFsRu4152aJCtWg'}, {'block_id': 'doxcnbQbemUXAZ2MF8ZJ7Vm4wLf', 'block_type': 12, 'bullet': {'elements': [{'text_run': {'content': '量级变化：25年2月四大中心日均人审量级#VALUE!万，对比规划#VALUE!%，对比基期12月#VALUE!%。', 'text_element_style': {'bold': True, 'inline_code': False, 'italic': False, 'strikethrough': False, 'underline': False}}}], 'style': {'align': 1, 'folded': False}}, 'children': ['doxcnEivIbM2lOJBpcVA65Ny4Hd', 'doxcn3F0DPyKgKL0Po8kvGNvRid'], 'parent_id': 'doxcnsDkYkQhHdxoDwbvQTwQ58g'}, {'block_id': 'doxcnEivIbM2lOJBpcVA65Ny4Hd', 'block_type': 12, 'bullet': {'elements': [{'text_run': {'content': 'vs规划（#VALUE!%）：', 'text_element_style': {'bold': True, 'inline_code': False, 'italic': False, 'strikethrough': False, 'underline': False}}}, {'text_run': {'content': '其中头条群组、垂直群组对比规划增长，其他群组量级对比规划均有下降。', 'text_element_style': {'bold': False, 'inline_code': False, 'italic': False, 'strikethrough': False, 'underline': False}}}, {'text_run': {'content': '【数据来源于表3】', 'text_element_style': {'background_color': 14, 'bold': False, 'inline_code': False, 'italic': False, 'strikethrough': False, 'underline': False}}}], 'style': {'align': 1, 'folded': False}}, 'parent_id': 'doxcnbQbemUXAZ2MF8ZJ7Vm4wLf'}, {'block_id': 'doxcn3F0DPyKgKL0Po8kvGNvRid', 'block_type': 12, 'bullet': {'elements': [{'text_run': {'content': 'vs12月（#VALUE!%）：', 'text_element_style': {'bold': True, 'inline_code': False, 'italic': False, 'strikethrough': False, 'underline': False}}}, {'text_run': {'content': \"其中新业务接入 #VALUE!%, 存量业务 群组月报%，主动降量#VALUE!%，分群组来看「'index':「'表格3':'A6:A14'」,'data':「'表格3':'I6:I14'」,'type':'S_3_+','drop':'合计'」侧量级环比增长，「'index':「'表格3':'A6:A14'」,'data':「'表格3':'I6:I14'」,'type':'S_3_-','drop':'合计'」组织量级均为下降；\", 'text_element_style': {'bold': False, 'inline_code': False, 'italic': False, 'strikethrough': False, 'underline': False}}}, {'text_run': {'content': '【数据来源于表3】', 'text_element_style': {'background_color': 14, 'bold': False, 'inline_code': False, 'italic': False, 'strikethrough': False, 'underline': False}}}], 'style': {'align': 1, 'folded': False}}, 'parent_id': 'doxcnbQbemUXAZ2MF8ZJ7Vm4wLf'}, {'block_id': 'doxcn6GaSGisna1HDZNj6TS4nUb', 'block_type': 12, 'bullet': {'elements': [{'text_run': {'content': '人力变化：截止25年3月四大中心合计人力 #VALUE!人 ', 'text_element_style': {'bold': True, 'inline_code': False, 'italic': False, 'strikethrough': False, 'underline': False}}}, {'text_run': {'content': ' （自营数据集1人/占比-%，X模式 数据集1人/占比-%，BPO数据集1人/占比-%，OSP折算人力 数据集1人/占比-%）', 'text_element_style': {'bold': False, 'inline_code': False, 'italic': False, 'strikethrough': False, 'underline': False}}}, {'text_run': {'content': '。', 'text_element_style': {'bold': True, 'inline_code': False, 'italic': False, 'strikethrough': False, 'underline': False}}}], 'style': {'align': 1, 'folded': False}}, 'children': ['doxcndyBy2bNdSv9dNEGpppMqfd', 'doxcneMFVvq6j6M6teTKzE2g3Bc'], 'parent_id': 'doxcnsDkYkQhHdxoDwbvQTwQ58g'}, {'block_id': 'doxcndyBy2bNdSv9dNEGpppMqfd', 'block_type': 12, 'bullet': {'elements': [{'text_run': {'content': 'vs规划（#VALUE!人，#VALUE!%）：', 'text_element_style': {'bold': True, 'inline_code': False, 'italic': False, 'strikethrough': False, 'underline': False}}}, {'text_run': {'content': '主要由于垂直群组、直播群组等影响。', 'text_element_style': {'bold': False, 'inline_code': False, 'italic': False, 'strikethrough': False, 'underline': False}}}], 'style': {'align': 1, 'folded': False}}, 'children': ['doxcnWvcMYuSB1nPP42ihEgIAwb'], 'parent_id': 'doxcn6GaSGisna1HDZNj6TS4nUb'}, {'block_id': 'doxcnWvcMYuSB1nPP42ihEgIAwb', 'block_type': 12, 'bullet': {'elements': [{'text_run': {'content': '人力结构 vs.规划：对比2月规划，整体自营24,495人→#VALUE!人', 'text_element_style': {'bold': False, 'inline_code': False, 'italic': False, 'strikethrough': False, 'underline': False}}}, {'text_run': {'content': '（#VALUE!人，-%）', 'text_element_style': {'bold': False, 'inline_code': False, 'italic': True, 'strikethrough': False, 'underline': False}}}, {'text_run': {'content': '，X模式4,760人→ #VALUE!人', 'text_element_style': {'bold': False, 'inline_code': False, 'italic': False, 'strikethrough': False, 'underline': False}}}, {'text_run': {'content': '（#VALUE!人，-%）', 'text_element_style': {'bold': False, 'inline_code': False, 'italic': True, 'strikethrough': False, 'underline': False}}}, {'text_run': {'content': '，BPO 17,096人→#VALUE!人', 'text_element_style': {'bold': False, 'inline_code': False, 'italic': False, 'strikethrough': False, 'underline': False}}}, {'text_run': {'content': '（#VALUE!人，-%）', 'text_element_style': {'bold': False, 'inline_code': False, 'italic': True, 'strikethrough': False, 'underline': False}}}, {'text_run': {'content': '，OSP折算人力8,347人→#VALUE!人', 'text_element_style': {'bold': False, 'inline_code': False, 'italic': False, 'strikethrough': False, 'underline': False}}}, {'text_run': {'content': '（#VALUE!人，-%）', 'text_element_style': {'bold': False, 'inline_code': False, 'italic': True, 'strikethrough': False, 'underline': False}}}, {'text_run': {'content': '。', 'text_element_style': {'bold': False, 'inline_code': False, 'italic': False, 'strikethrough': False, 'underline': False}}}, {'text_run': {'content': '【数据来源于表1】', 'text_element_style': {'background_color': 14, 'bold': False, 'inline_code': False, 'italic': False, 'strikethrough': False, 'underline': False}}}], 'style': {'align': 1, 'folded': False}}, 'parent_id': 'doxcndyBy2bNdSv9dNEGpppMqfd'}, {'block_id': 'doxcneMFVvq6j6M6teTKzE2g3Bc', 'block_type': 12, 'bullet': {'elements': [{'text_run': {'content': 'vs12月（#VALUE!人，#VALUE!%）', 'text_element_style': {'bold': True, 'inline_code': False, 'italic': False, 'strikethrough': False, 'underline': False}}}], 'style': {'align': 1, 'folded': False}}, 'children': ['doxcn49WZwmBDo51nX71JW0BKFe', 'doxcnV2pT1adGF85kWPZdws6bDh'], 'parent_id': 'doxcn6GaSGisna1HDZNj6TS4nUb'}, {'block_id': 'doxcn49WZwmBDo51nX71JW0BKFe', 'block_type': 12, 'bullet': {'elements': [{'text_run': {'content': '人力结构 vs.12月：整体自营数据集1人→数据集1人', 'text_element_style': {'bold': False, 'inline_code': False, 'italic': False, 'strikethrough': False, 'underline': False}}}, {'text_run': {'content': '（#VALUE!人，-%）', 'text_element_style': {'bold': False, 'inline_code': False, 'italic': True, 'strikethrough': False, 'underline': False}}}, {'text_run': {'content': '，X模式数据集1人→数据集1人', 'text_element_style': {'bold': False, 'inline_code': False, 'italic': False, 'strikethrough': False, 'underline': False}}}, {'text_run': {'content': '（#VALUE!人，-%）', 'text_element_style': {'bold': False, 'inline_code': False, 'italic': True, 'strikethrough': False, 'underline': False}}}, {'text_run': {'content': '，BPO 数据集1人→数据集1人', 'text_element_style': {'bold': False, 'inline_code': False, 'italic': False, 'strikethrough': False, 'underline': False}}}, {'text_run': {'content': '（#VALUE!人，-%）', 'text_element_style': {'bold': False, 'inline_code': False, 'italic': True, 'strikethrough': False, 'underline': False}}}, {'text_run': {'content': '，OSP折算人力数据集1人→数据集1人', 'text_element_style': {'bold': False, 'inline_code': False, 'italic': False, 'strikethrough': False, 'underline': False}}}, {'text_run': {'content': '（#VALUE!人，-%）', 'text_element_style': {'bold': False, 'inline_code': False, 'italic': True, 'strikethrough': False, 'underline': False}}}, {'text_run': {'content': '。', 'text_element_style': {'bold': False, 'inline_code': False, 'italic': False, 'strikethrough': False, 'underline': False}}}], 'style': {'align': 1, 'folded': False}}, 'parent_id': 'doxcneMFVvq6j6M6teTKzE2g3Bc'}, {'block_id': 'doxcnV2pT1adGF85kWPZdws6bDh', 'block_type': 12, 'bullet': {'elements': [{'text_run': {'content': '整体净变动来看，各组织人力净变动比：YY >YY >YY> YY >YY >YY > YY >YY >YY，拆分影响来看人力关键变化如下：', 'text_element_style': {'background_color': 5, 'bold': True, 'inline_code': False, 'italic': False, 'strikethrough': False, 'underline': False}}}, {'text_run': {'content': ' 【数据来源于表5】', 'text_element_style': {'background_color': 14, 'bold': True, 'inline_code': False, 'italic': False, 'strikethrough': False, 'underline': False}}}], 'style': {'align': 1, 'folded': False}}, 'children': ['doxcnxyJMmEjMdcKjtYHatbFjTE', 'doxcnsWxVCGtXmrJ7Ovk7qIu5WX', 'doxcnu4hFRocLd2ok0fHrYjhZsX', 'doxcnV2JxoPMFi0opS0U2EbfZvg'], 'parent_id': 'doxcneMFVvq6j6M6teTKzE2g3Bc'}, {'block_id': 'doxcnxyJMmEjMdcKjtYHatbFjTE', 'block_type': 13, 'ordered': {'elements': [{'text_run': {'content': '上游需求人力（#VALUE!人）：2月对比12月新增业务需求人力-人，存量业务人审量级变动影响#VALUE!人，众包替代影响#VALUE!人； ', 'text_element_style': {'bold': False, 'inline_code': False, 'italic': False, 'strikethrough': False, 'underline': False}}}, {'text_run': {'content': '【数据来源于表5】', 'text_element_style': {'background_color': 14, 'bold': True, 'inline_code': False, 'italic': False, 'strikethrough': False, 'underline': False}}}], 'style': {'align': 1, 'folded': False, 'sequence': '1'}}, 'parent_id': 'doxcnV2pT1adGF85kWPZdws6bDh'}, {'block_id': 'doxcnsWxVCGtXmrJ7Ovk7qIu5WX', 'block_type': 13, 'children': ['doxcnTZyfmD29f4xdroxRA5rrSc'], 'ordered': {'elements': [{'text_run': {'content': '主动管理动作（0人，0.0%）：3月对比12月，CQC推进主动降量影响人力#VALUE!人（-%），整体提效影响#VALUE!人（-%），非一线优化影响#VALUE!人（-%）；', 'text_element_style': {'bold': False, 'inline_code': False, 'italic': False, 'strikethrough': False, 'underline': False}}}, {'text_run': {'content': '【数据来源于表5】', 'text_element_style': {'background_color': 14, 'bold': True, 'inline_code': False, 'italic': False, 'strikethrough': False, 'underline': False}}}], 'style': {'align': 1, 'folded': False, 'sequence': 'auto'}}, 'parent_id': 'doxcnV2pT1adGF85kWPZdws6bDh'}, {'block_id': 'doxcnTZyfmD29f4xdroxRA5rrSc', 'block_type': 12, 'bullet': {'elements': [{'text_run': {'content': '各组织管理动作人力收益占比：', 'text_element_style': {'background_color': 5, 'bold': True, 'inline_code': False, 'italic': False, 'strikethrough': False, 'underline': False}}}, {'text_run': {'content': ' ', 'text_element_style': {'background_color': 5, 'bold': False, 'inline_code': False, 'italic': False, 'strikethrough': False, 'underline': False}}}, {'text_run': {'content': '抖音群组 > 直播群组 > 头条群组 > 垂直群组 > 广告 > 生活服务 > 电商 > 运营支持中心 > 商业安全-合计', 'text_element_style': {'background_color': 5, 'bold': True, 'inline_code': False, 'italic': False, 'strikethrough': False, 'underline': False}}}, {'text_run': {'content': '【数据来源于表5】', 'text_element_style': {'background_color': 14, 'bold': True, 'inline_code': False, 'italic': False, 'strikethrough': False, 'underline': False}}}], 'style': {'align': 1, 'folded': False}}, 'parent_id': 'doxcnsWxVCGtXmrJ7Ovk7qIu5WX'}, {'block_id': 'doxcnu4hFRocLd2ok0fHrYjhZsX', 'block_type': 13, 'ordered': {'elements': [{'text_run': {'content': '短期无产出人力（-人）：截止3月底，用工模式迁移影响双跑人力#VALUE!人，其他待调整/储备新人#VALUE!人；', 'text_element_style': {'bold': False, 'inline_code': False, 'italic': False, 'strikethrough': False, 'underline': False}}}, {'text_run': {'content': '【数据来源于表5】', 'text_element_style': {'background_color': 14, 'bold': True, 'inline_code': False, 'italic': False, 'strikethrough': False, 'underline': False}}}], 'style': {'align': 1, 'folded': False, 'sequence': 'auto'}}, 'parent_id': 'doxcnV2pT1adGF85kWPZdws6bDh'}, {'block_id': 'doxcnV2JxoPMFi0opS0U2EbfZvg', 'block_type': 13, 'ordered': {'elements': [{'text_run': {'content': '其他（#VALUE!人）：12月存在双跑&短期冗余人力#VALUE!人；', 'text_element_style': {'bold': False, 'inline_code': False, 'italic': True, 'strikethrough': False, 'underline': False}}}, {'text_run': {'content': '【数据来源于表5】', 'text_element_style': {'background_color': 14, 'bold': True, 'inline_code': False, 'italic': True, 'strikethrough': False, 'underline': False}}}], 'style': {'align': 1, 'folded': False, 'sequence': 'auto'}}, 'parent_id': 'doxcnV2pT1adGF85kWPZdws6bDh'}, {'block_id': 'doxcntyMUUaClIyc9CrUMwXKxUe', 'block_type': 12, 'bullet': {'elements': [{'text_run': {'content': '消费环节情况参考', 'text_element_style': {'bold': False, 'inline_code': False, 'italic': True, 'strikethrough': False, 'underline': True}}}, {'text_run': {'content': ' ', 'text_element_style': {'bold': False, 'inline_code': False, 'italic': True, 'strikethrough': False, 'text_color': 5, 'underline': True}}}, {'text_run': {'content': '【四、消费环节关键指标】', 'text_element_style': {'bold': True, 'inline_code': False, 'italic': True, 'strikethrough': False, 'text_color': 5, 'underline': True}}}], 'style': {'align': 1, 'folded': False}}, 'parent_id': 'doxcnwuXCBKrJFsRu4152aJCtWg'}, {'block_id': 'doxcn7wqrN4LWuurjD5aaMqfnoc', 'block_type': 12, 'bullet': {'elements': [{'text_run': {'content': 'Highlight', 'text_element_style': {'background_color': 4, 'bold': True, 'inline_code': False, 'italic': False, 'strikethrough': False, 'text_color': 4, 'underline': False}}}], 'style': {'align': 1, 'folded': False}}, 'children': ['doxcnx8AnGJwrJ7Dz2df0fm30Tf'], 'parent_id': 'doxcnwuXCBKrJFsRu4152aJCtWg'}, {'block_id': 'doxcnx8AnGJwrJ7Dz2df0fm30Tf', 'block_type': 12, 'bullet': {'elements': [{'text_run': {'content': '人工分析补充', 'text_element_style': {'background_color': 10, 'bold': True, 'inline_code': False, 'italic': False, 'strikethrough': False, 'underline': False}}}], 'style': {'align': 1, 'folded': False}}, 'parent_id': 'doxcn7wqrN4LWuurjD5aaMqfnoc'}, {'block_id': 'doxcnTpL3EVOw3lrEkoaoQ1F7Zb', 'block_type': 12, 'bullet': {'elements': [{'text_run': {'content': 'Lowlight/困难点/关注点', 'text_element_style': {'background_color': 2, 'bold': True, 'inline_code': False, 'italic': False, 'strikethrough': False, 'text_color': 2, 'underline': False}}}], 'style': {'align': 1, 'folded': False}}, 'children': ['doxcnIEOAVMQZbg8rM3jCRmUJ1q'], 'parent_id': 'doxcnwuXCBKrJFsRu4152aJCtWg'}, {'block_id': 'doxcnIEOAVMQZbg8rM3jCRmUJ1q', 'block_type': 12, 'bullet': {'elements': [{'text_run': {'content': '人工分析补充', 'text_element_style': {'background_color': 10, 'bold': True, 'inline_code': False, 'italic': False, 'strikethrough': False, 'underline': False}}}], 'style': {'align': 1, 'folded': False}}, 'parent_id': 'doxcnTpL3EVOw3lrEkoaoQ1F7Zb'}, {'block_id': 'doxcnLUYBc5SGqU46xAiKFNg5bb', 'block_type': 5, 'heading3': {'elements': [{'text_run': {'content': '二、量/效/人情况', 'text_element_style': {'bold': False, 'inline_code': False, 'italic': False, 'strikethrough': False, 'underline': False}}}], 'style': {'align': 1, 'folded': False}}, 'parent_id': 'W8XYdLK9JoITNRxYBvKcmwHvnRR'}, {'block_id': 'doxcnt4YDiuMpQDd9JcY4dziF4f', 'block_type': 6, 'heading4': {'elements': [{'text_run': {'content': '整体人力变化情况拆分', 'text_element_style': {'bold': False, 'inline_code': False, 'italic': False, 'strikethrough': False, 'underline': False}}}], 'style': {'align': 1, 'folded': False}}, 'parent_id': 'W8XYdLK9JoITNRxYBvKcmwHvnRR'}, {'block_id': 'doxcnbgtdVPHiTV0i7McIx0faxe', 'block_type': 7, 'children': ['doxcn5RwCERgegtFFoyUhpQGNNc', 'doxcnLJGUi5OsDbDPlcmydJVVAd'], 'heading5': {'elements': [{'text_run': {'content': '2月人力vs规划变动情况 【阅评】', 'text_element_style': {'background_color': 15, 'bold': False, 'inline_code': False, 'italic': False, 'strikethrough': False, 'underline': False}}}], 'style': {'align': 1, 'folded': False}}, 'parent_id': 'W8XYdLK9JoITNRxYBvKcmwHvnRR'}, {'block_id': 'doxcn5RwCERgegtFFoyUhpQGNNc', 'block_type': 19, 'callout': {'border_color': 5, 'emoji_id': 'writing_hand'}, 'children': ['doxcn51dVfpuhVghhIzN9znOKdg', 'doxcnQKrZf7g6Pwbxj8Nt1r8YRg', 'doxcnUFJtfgsqMwgwm5k59I1gPv'], 'parent_id': 'doxcnbgtdVPHiTV0i7McIx0faxe'}, {'block_id': 'doxcn51dVfpuhVghhIzN9znOKdg', 'block_type': 2, 'parent_id': 'doxcn5RwCERgegtFFoyUhpQGNNc', 'text': {'elements': [{'text_run': {'content': '总结概述（2月vs规划）：', 'text_element_style': {'background_color': 15, 'bold': True, 'inline_code': False, 'italic': False, 'strikethrough': False, 'underline': False}}}], 'style': {'align': 1, 'folded': False}}}, {'block_id': 'doxcnQKrZf7g6Pwbxj8Nt1r8YRg', 'block_type': 13, 'ordered': {'elements': [{'text_run': {'content': '2月vs规划来看，整体人审量级#VALUE!；', 'text_element_style': {'bold': True, 'inline_code': False, 'italic': False, 'strikethrough': False, 'underline': False}}}], 'style': {'align': 1, 'folded': False, 'sequence': '1'}}, 'parent_id': 'doxcn5RwCERgegtFFoyUhpQGNNc'}, {'block_id': 'doxcnUFJtfgsqMwgwm5k59I1gPv', 'block_type': 13, 'children': ['doxcnCTUpxiYUq46zhtyGxz19gh', 'doxcnnqGl1J49u0sYGQmBclqYEh', 'doxcnc3yELSeyjQeKsL7hCbBlpg', 'doxcnWGnhkaKSa9ivABZeQHcoEb'], 'ordered': {'elements': [{'text_run': {'content': '截止2月底，四大中心合计人力#VALUE!人 ,对比2月规划HC#VALUE!人（自营#VALUE!人，X模式 #VALUE!人，BPO #VALUE!人），#VALUE!%；', 'text_element_style': {'bold': True, 'inline_code': False, 'italic': False, 'strikethrough': False, 'underline': False}}}], 'style': {'align': 1, 'folded': False, 'sequence': 'auto'}}, 'parent_id': 'doxcn5RwCERgegtFFoyUhpQGNNc'}, {'block_id': 'doxcnCTUpxiYUq46zhtyGxz19gh', 'block_type': 13, 'ordered': {'elements': [{'text_run': {'content': '新业务&量级变化影响（#VALUE!人）：主要为{\"RAG\":\"各个群组量级变化影响人力的原因\"}；', 'text_element_style': {'bold': False, 'inline_code': False, 'italic': False, 'strikethrough': False, 'underline': False}}}], 'style': {'align': 1, 'folded': False, 'sequence': '1'}}, 'parent_id': 'doxcnUFJtfgsqMwgwm5k59I1gPv'}, {'block_id': 'doxcnnqGl1J49u0sYGQmBclqYEh', 'block_type': 13, 'ordered': {'elements': [{'text_run': {'content': '业务迁移节奏影响（408人）：主要为{\"RAG\":\"各个群组迁移节奏影响人力的原因\"}；', 'text_element_style': {'bold': False, 'inline_code': False, 'italic': False, 'strikethrough': False, 'underline': False}}}], 'style': {'align': 1, 'folded': False, 'sequence': 'auto'}}, 'parent_id': 'doxcnUFJtfgsqMwgwm5k59I1gPv'}, {'block_id': 'doxcnc3yELSeyjQeKsL7hCbBlpg', 'block_type': 13, 'ordered': {'elements': [{'text_run': {'content': '招聘差异影响（-294人）：主要为{\"RAG\":\"各个群组招聘差异影响人力的原因\"}；', 'text_element_style': {'bold': False, 'inline_code': False, 'italic': False, 'strikethrough': False, 'underline': False}}}], 'style': {'align': 1, 'folded': False, 'sequence': 'auto'}}, 'parent_id': 'doxcnUFJtfgsqMwgwm5k59I1gPv'}, {'block_id': 'doxcnWGnhkaKSa9ivABZeQHcoEb', 'block_type': 13, 'ordered': {'elements': [{'text_run': {'content': 'CQC主动举措影响（-842人）：主要为{\"RAG\":\"各个群组主动举措影响人力的原因\"}；', 'text_element_style': {'bold': False, 'inline_code': False, 'italic': False, 'strikethrough': False, 'underline': False}}}], 'style': {'align': 1, 'folded': False, 'sequence': 'auto'}}, 'parent_id': 'doxcnUFJtfgsqMwgwm5k59I1gPv'}, {'block_id': 'doxcnLJGUi5OsDbDPlcmydJVVAd', 'block_type': 30, 'parent_id': 'doxcnbgtdVPHiTV0i7McIx0faxe', 'sheet': {'token': 'D9XismEq0hOGoXttIOlcj4ponWe_8HwYgY'}}, {'block_id': 'doxcnVCDmM8LoM8igP79VlgYRpb', 'block_type': 7, 'children': ['doxcnn7AH19HDZjUke7OpxlWfXg', 'doxcnulze0Je0qEg1P3YjViP2Vb'], 'heading5': {'elements': [{'text_run': {'content': '2月人力vs12月变动情况', 'text_element_style': {'bold': False, 'inline_code': False, 'italic': False, 'strikethrough': False, 'underline': False}}}], 'style': {'align': 1, 'folded': False}}, 'parent_id': 'W8XYdLK9JoITNRxYBvKcmwHvnRR'}, {'block_id': 'doxcnn7AH19HDZjUke7OpxlWfXg', 'block_type': 19, 'callout': {'border_color': 5, 'emoji_id': 'writing_hand'}, 'children': ['doxcn5bEp4UMaeTRloAJsiVsTDf', 'doxcnnRkZGYf4y4AfwDwfB0xfhf', 'doxcnbmBtmBlJFX4NF4akTv95mD', 'doxcn3fv1KhaJRTefNypmeFQqpc'], 'parent_id': 'doxcnVCDmM8LoM8igP79VlgYRpb'}, {'block_id': 'doxcn5bEp4UMaeTRloAJsiVsTDf', 'block_type': 2, 'parent_id': 'doxcnn7AH19HDZjUke7OpxlWfXg', 'text': {'elements': [{'text_run': {'content': '总结概述：', 'text_element_style': {'bold': True, 'inline_code': False, 'italic': False, 'strikethrough': False, 'underline': False}}}], 'style': {'align': 1, 'folded': False}}}, {'block_id': 'doxcnnRkZGYf4y4AfwDwfB0xfhf', 'block_type': 13, 'ordered': {'elements': [{'text_run': {'content': '2月vs12月来看，整体人审量级#VALUE!（日均#VALUE!→日均#VALUE!），其中新业务接入#VALUE!%, 存量业务增长群组月报%，主动降量#VALUE!%；', 'text_element_style': {'bold': True, 'inline_code': False, 'italic': False, 'strikethrough': False, 'underline': False}}}], 'style': {'align': 1, 'folded': False, 'sequence': '1'}}, 'parent_id': 'doxcnn7AH19HDZjUke7OpxlWfXg'}, {'block_id': 'doxcnbmBtmBlJFX4NF4akTv95mD', 'block_type': 13, 'children': ['doxcnP1wBxhiKoFJTzLv1fXzZYg', 'doxcnNao6wkLtlryu0P8DSOR9Ve', 'doxcnlnUbOe6LoLznYUCyjdLlxf', 'doxcnU7tsvgHr65TPg6w7p0GGmf'], 'ordered': {'elements': [{'text_run': {'content': '截止2月底，四大中心合计人力#VALUE!人 ,对比12月下降#VALUE!人， #VALUE!%，拆分各模块变化如下：【数据来源于表5】', 'text_element_style': {'bold': True, 'inline_code': False, 'italic': False, 'strikethrough': False, 'underline': False}}}], 'style': {'align': 1, 'folded': False, 'sequence': 'auto'}}, 'parent_id': 'doxcnn7AH19HDZjUke7OpxlWfXg'}, {'block_id': 'doxcnP1wBxhiKoFJTzLv1fXzZYg', 'block_type': 13, 'ordered': {'elements': [{'text_run': {'content': '上游需求人力（', 'text_element_style': {'bold': False, 'inline_code': False, 'italic': False, 'strikethrough': False, 'underline': False}}}, {'text_run': {'content': ' #VALUE!', 'text_element_style': {'bold': True, 'inline_code': False, 'italic': False, 'strikethrough': False, 'underline': False}}}, {'text_run': {'content': '人）：2月对比12月新增业务需求人力 ', 'text_element_style': {'bold': False, 'inline_code': False, 'italic': False, 'strikethrough': False, 'underline': False}}}, {'text_run': {'content': '- ', 'text_element_style': {'bold': True, 'inline_code': False, 'italic': False, 'strikethrough': False, 'underline': False}}}, {'text_run': {'content': '人，存量业务人审量级变动影响 ', 'text_element_style': {'bold': False, 'inline_code': False, 'italic': False, 'strikethrough': False, 'underline': False}}}, {'text_run': {'content': ' #VALUE!', 'text_element_style': {'bold': True, 'inline_code': False, 'italic': False, 'strikethrough': False, 'underline': False}}}, {'text_run': {'content': '人，众包替代影响#VALUE!人；', 'text_element_style': {'bold': False, 'inline_code': False, 'italic': False, 'strikethrough': False, 'underline': False}}}], 'style': {'align': 1, 'folded': False, 'sequence': '1'}}, 'parent_id': 'doxcnbmBtmBlJFX4NF4akTv95mD'}, {'block_id': 'doxcnNao6wkLtlryu0P8DSOR9Ve', 'block_type': 13, 'ordered': {'elements': [{'text_run': {'content': '主动管理动作（0人）：2月对比12月，CQC推进主动降量影响人力 #VALUE!人，整体提效影响#VALUE!人，非一线优化影响#VALUE!人；', 'text_element_style': {'bold': False, 'inline_code': False, 'italic': False, 'strikethrough': False, 'underline': False}}}], 'style': {'align': 1, 'folded': False, 'sequence': 'auto'}}, 'parent_id': 'doxcnbmBtmBlJFX4NF4akTv95mD'}, {'block_id': 'doxcnlnUbOe6LoLznYUCyjdLlxf', 'block_type': 13, 'ordered': {'elements': [{'text_run': {'content': '短期无产出人力（', 'text_element_style': {'bold': False, 'inline_code': False, 'italic': False, 'strikethrough': False, 'underline': False}}}, {'text_run': {'content': ' -', 'text_element_style': {'bold': True, 'inline_code': False, 'italic': False, 'strikethrough': False, 'underline': False}}}, {'text_run': {'content': '人）：截止2月底，用工模式迁移影响双跑人力 #VALUE!人，其他待调整/储备新人#VALUE!人；', 'text_element_style': {'bold': False, 'inline_code': False, 'italic': False, 'strikethrough': False, 'underline': False}}}], 'style': {'align': 1, 'folded': False, 'sequence': 'auto'}}, 'parent_id': 'doxcnbmBtmBlJFX4NF4akTv95mD'}, {'block_id': 'doxcnU7tsvgHr65TPg6w7p0GGmf', 'block_type': 13, 'ordered': {'elements': [{'text_run': {'content': '其他（', 'text_element_style': {'bold': False, 'inline_code': False, 'italic': True, 'strikethrough': False, 'underline': False}}}, {'text_run': {'content': ' #VALUE!', 'text_element_style': {'bold': True, 'inline_code': False, 'italic': True, 'strikethrough': False, 'underline': False}}}, {'text_run': {'content': '人）：12月存在双跑&短期冗余人力', 'text_element_style': {'bold': False, 'inline_code': False, 'italic': True, 'strikethrough': False, 'underline': False}}}, {'text_run': {'content': ' #VALUE!', 'text_element_style': {'bold': True, 'inline_code': False, 'italic': True, 'strikethrough': False, 'underline': False}}}, {'text_run': {'content': '人；', 'text_element_style': {'bold': False, 'inline_code': False, 'italic': True, 'strikethrough': False, 'underline': False}}}], 'style': {'align': 1, 'folded': False, 'sequence': 'auto'}}, 'parent_id': 'doxcnbmBtmBlJFX4NF4akTv95mD'}, {'block_id': 'doxcn3fv1KhaJRTefNypmeFQqpc', 'block_type': 13, 'ordered': {'elements': [{'text_run': {'content': '2月CQC综合万case需求人力#VALUE!，环比12月下降-%（受人审效率 & 业务量级结构影响）；', 'text_element_style': {'bold': True, 'inline_code': False, 'italic': False, 'strikethrough': False, 'underline': False}}}], 'style': {'align': 1, 'folded': False, 'sequence': 'auto'}}, 'parent_id': 'doxcnn7AH19HDZjUke7OpxlWfXg'}, {'block_id': 'doxcnulze0Je0qEg1P3YjViP2Vb', 'block_type': 30, 'parent_id': 'doxcnVCDmM8LoM8igP79VlgYRpb', 'sheet': {'token': 'D9XismEq0hOGoXttIOlcj4ponWe_rTNVIw'}}, {'block_id': 'doxcn4tGsFMNCiySxl9GCF7Kymc', 'block_type': 6, 'children': ['doxcnweiwcMlXftFjHTu08uReSg', 'doxcn9RXxqPHiEsGI9ceS62LvLf'], 'heading4': {'elements': [{'text_run': {'content': '2月量级变动详细情况', 'text_element_style': {'bold': True, 'inline_code': False, 'italic': False, 'strikethrough': False, 'underline': False}}}], 'style': {'align': 1, 'folded': False}}, 'parent_id': 'W8XYdLK9JoITNRxYBvKcmwHvnRR'}, {'block_id': 'doxcnweiwcMlXftFjHTu08uReSg', 'block_type': 19, 'callout': {'border_color': 5, 'emoji_id': 'writing_hand'}, 'children': ['doxcntYwpSLF2Gl0Nje7wl2me4d', 'doxcn5Wx1FuL1D7B2xk38oAwbUX', 'doxcnPTOPPj8qOu4iJ7wJ8012zf', 'doxcnp6QRSGbjt5YnO5LkqlYx8b', 'doxcnpDbfOykDjT3z3BRtsErhxT'], 'parent_id': 'doxcn4tGsFMNCiySxl9GCF7Kymc'}, {'block_id': 'doxcntYwpSLF2Gl0Nje7wl2me4d', 'block_type': 2, 'parent_id': 'doxcnweiwcMlXftFjHTu08uReSg', 'text': {'elements': [{'text_run': {'content': '总结概述（2月vs12月）：', 'text_element_style': {'bold': True, 'inline_code': False, 'italic': False, 'strikethrough': False, 'underline': False}}}], 'style': {'align': 1, 'folded': False}}}, {'block_id': 'doxcn5Wx1FuL1D7B2xk38oAwbUX', 'block_type': 2, 'parent_id': 'doxcnweiwcMlXftFjHTu08uReSg', 'text': {'elements': [{'text_run': {'content': '总进审量/发文量：', 'text_element_style': {'bold': True, 'inline_code': False, 'italic': False, 'strikethrough': False, 'underline': False}}}, {'text_run': {'content': '环比12月CQC承接业务产品总进审/发文量#VALUE!，其中主要为抖音群组 #VALUE!，直播群组 #VALUE!，头条群组 #VALUE!；', 'text_element_style': {'bold': False, 'inline_code': False, 'italic': False, 'strikethrough': False, 'underline': False}}}, {'text_run': {'content': '【数据来源于表3】', 'text_element_style': {'background_color': 14, 'bold': False, 'inline_code': False, 'italic': False, 'strikethrough': False, 'underline': False}}}], 'style': {'align': 1, 'folded': False}}}, {'block_id': 'doxcnPTOPPj8qOu4iJ7wJ8012zf', 'block_type': 2, 'children': ['doxcnBkoyigEjwnQwV6djIpRMxg', 'doxcnh2NDMlYbA95Az4mVWkrNtd', 'doxcnG33W996AclxNQajqeNDPkd'], 'parent_id': 'doxcnweiwcMlXftFjHTu08uReSg', 'text': {'elements': [{'text_run': {'content': '人审量级：', 'text_element_style': {'bold': True, 'inline_code': False, 'italic': False, 'strikethrough': False, 'underline': False}}}, {'text_run': {'content': '2月环比12月CQC整体人审&标注量级环比#VALUE!（其中新业务接入影响#VALUE!, 存量业务增长#VALUE!，主动降量影响#VALUE!），头条群组、垂直群组人审量级环比增长,抖音群组、直播群组、商安-广告经营、商安-生活服务、商安-电商、运营支持中心人审量级环比下降', 'text_element_style': {'bold': False, 'inline_code': False, 'italic': False, 'strikethrough': False, 'underline': False}}}, {'text_run': {'content': '【数据来源于表3】', 'text_element_style': {'background_color': 14, 'bold': False, 'inline_code': False, 'italic': False, 'strikethrough': False, 'underline': False}}}], 'style': {'align': 1, 'folded': False}}}, {'block_id': 'doxcnBkoyigEjwnQwV6djIpRMxg', 'block_type': 2, 'children': ['doxcn6EOyfDWIc07xAQqqD1kYqe', 'doxcnf2GfmDwGE9GglCbzyYfQug', 'doxcnwVKhk42dwV1iUdPY5Gfpje'], 'parent_id': 'doxcnPTOPPj8qOu4iJ7wJ8012zf', 'text': {'elements': [{'text_run': {'content': '新业务接入：', 'text_element_style': {'bold': True, 'inline_code': False, 'italic': False, 'strikethrough': False, 'underline': False}}}, {'text_run': {'content': '新增接入量级合计群组月报/日，其中商安-电商、头条群组、商安-广告经营接入量级较多；', 'text_element_style': {'bold': False, 'inline_code': False, 'italic': False, 'strikethrough': False, 'underline': False}}}, {'text_run': {'content': '【数据来源于表3】', 'text_element_style': {'background_color': 14, 'bold': False, 'inline_code': False, 'italic': False, 'strikethrough': False, 'underline': False}}}], 'style': {'align': 1, 'folded': False}}}, {'block_id': 'doxcn6EOyfDWIc07xAQqqD1kYqe', 'block_type': 2, 'parent_id': 'doxcnBkoyigEjwnQwV6djIpRMxg', 'text': {'elements': [{'text_run': {'content': '商安-电商（66138，14.93%）：主要为{\"RAG\":\"新业务接入量级原因\"}；', 'text_element_style': {'bold': False, 'inline_code': False, 'italic': False, 'strikethrough': False, 'underline': False}}}], 'style': {'align': 1, 'folded': False}}}, {'block_id': 'doxcnf2GfmDwGE9GglCbzyYfQug', 'block_type': 2, 'parent_id': 'doxcnBkoyigEjwnQwV6djIpRMxg', 'text': {'elements': [{'text_run': {'content': '头条群组（5380，0.63%）：主要为{\"RAG\":\"新业务接入量级原因\"}；', 'text_element_style': {'bold': False, 'inline_code': False, 'italic': False, 'strikethrough': False, 'underline': False}}}], 'style': {'align': 1, 'folded': False}}}, {'block_id': 'doxcnwVKhk42dwV1iUdPY5Gfpje', 'block_type': 2, 'parent_id': 'doxcnBkoyigEjwnQwV6djIpRMxg', 'text': {'elements': [{'text_run': {'content': '商安-广告经营（864，0.10%）：主要为{\"RAG\":\"新业务接入量级原因\"}；', 'text_element_style': {'bold': False, 'inline_code': False, 'italic': False, 'strikethrough': False, 'underline': False}}}], 'style': {'align': 1, 'folded': False}}}, {'block_id': 'doxcnh2NDMlYbA95Az4mVWkrNtd', 'block_type': 2, 'parent_id': 'doxcnPTOPPj8qOu4iJ7wJ8012zf', 'text': {'elements': [{'text_run': {'content': '主动降量：合计主动降量群组月报/日，从各组织主动降量占比来看：商安-生活服务 > 抖音群组 > 直播群组 > 垂直群组 > 运营支持中心 > 商安-广告经营 > 商安-电商 > 头条群组', 'text_element_style': {'bold': True, 'inline_code': False, 'italic': False, 'strikethrough': False, 'underline': False}}}], 'style': {'align': 1, 'folded': False}}}, {'block_id': 'doxcnG33W996AclxNQajqeNDPkd', 'block_type': 2, 'parent_id': 'doxcnPTOPPj8qOu4iJ7wJ8012zf', 'text': {'elements': [{'text_run': {'content': '', 'text_element_style': {'bold': False, 'inline_code': False, 'italic': False, 'strikethrough': False, 'underline': False}}}], 'style': {'align': 1, 'folded': False}}}, {'block_id': 'doxcnp6QRSGbjt5YnO5LkqlYx8b', 'block_type': 2, 'parent_id': 'doxcnweiwcMlXftFjHTu08uReSg', 'text': {'elements': [{'text_run': {'content': 'Highlight', 'text_element_style': {'background_color': 4, 'bold': True, 'inline_code': False, 'italic': False, 'strikethrough': False, 'text_color': 4, 'underline': False}}}], 'style': {'align': 1, 'folded': False}}}, {'block_id': 'doxcnpDbfOykDjT3z3BRtsErhxT', 'block_type': 12, 'bullet': {'elements': [{'text_run': {'content': '主动降量持续推进：', 'text_element_style': {'bold': True, 'inline_code': False, 'italic': False, 'strikethrough': False, 'underline': False}}}, {'text_run': {'content': '整体CQC 2月主动降量~群组月报，占量级比例#VALUE!；', 'text_element_style': {'bold': False, 'inline_code': False, 'italic': False, 'strikethrough': False, 'underline': False}}}, {'text_run': {'content': '【数据来源于表3】', 'text_element_style': {'background_color': 14, 'bold': False, 'inline_code': False, 'italic': False, 'strikethrough': False, 'underline': False}}}], 'style': {'align': 1, 'folded': False}}, 'children': ['doxcnUcYQM26t9oJ74wgQeLrrhc', 'doxcnAj1LbhnAdE5ZerCZIUZSub', 'doxcnF6xn98SfqrjjFT44iXRjib'], 'parent_id': 'doxcnweiwcMlXftFjHTu08uReSg'}, {'block_id': 'doxcnUcYQM26t9oJ74wgQeLrrhc', 'block_type': 12, 'bullet': {'elements': [{'text_run': {'content': '商安-生活服务（28000，2.2%）：主要为{\"RAG\":\"主动降量持续推进原因\"}；', 'text_element_style': {'bold': False, 'inline_code': False, 'italic': False, 'strikethrough': False, 'underline': False}}}], 'style': {'align': 1, 'folded': False}}, 'parent_id': 'doxcnpDbfOykDjT3z3BRtsErhxT'}, {'block_id': 'doxcnAj1LbhnAdE5ZerCZIUZSub', 'block_type': 12, 'bullet': {'elements': [{'text_run': {'content': '抖音群组（0，0.0%）：主要为{\"RAG\":\"主动降量持续推进原因\"}；', 'text_element_style': {'bold': False, 'inline_code': False, 'italic': False, 'strikethrough': False, 'underline': False}}}], 'style': {'align': 1, 'folded': False}}, 'parent_id': 'doxcnpDbfOykDjT3z3BRtsErhxT'}, {'block_id': 'doxcnF6xn98SfqrjjFT44iXRjib', 'block_type': 12, 'bullet': {'elements': [{'text_run': {'content': '直播群组（0，0.0%）：主要为{\"RAG\":\"主动降量持续推进原因\"}；', 'text_element_style': {'bold': False, 'inline_code': False, 'italic': False, 'strikethrough': False, 'underline': False}}}], 'style': {'align': 1, 'folded': False}}, 'parent_id': 'doxcnpDbfOykDjT3z3BRtsErhxT'}, {'block_id': 'doxcn9RXxqPHiEsGI9ceS62LvLf', 'block_type': 30, 'parent_id': 'doxcn4tGsFMNCiySxl9GCF7Kymc', 'sheet': {'token': 'D9XismEq0hOGoXttIOlcj4ponWe_QEEtvY'}}, {'block_id': 'doxcnYyvLoFOf6Oi5fBfJMPi0bb', 'block_type': 6, 'children': ['doxcn12YdrBTNqEVMP31nJD7uQc', 'doxcnaFFm4cgJBRfYspOUdxMFDd'], 'heading4': {'elements': [{'text_run': {'content': '2月关键效率变动情况', 'text_element_style': {'bold': False, 'inline_code': False, 'italic': False, 'strikethrough': False, 'underline': False}}}], 'style': {'align': 1, 'folded': False}}, 'parent_id': 'W8XYdLK9JoITNRxYBvKcmwHvnRR'}, {'block_id': 'doxcn12YdrBTNqEVMP31nJD7uQc', 'block_type': 19, 'callout': {'border_color': 5, 'emoji_id': 'lower_left_crayon'}, 'children': ['doxcne7gH06jH0shKDBxzDwmaNg', 'doxcnPzf5FWoYgX397UQ5bGz6GO', 'doxcn6H4R2acBzc8jPo4WYwrXDe', 'doxcnuITtBD0V0qPs4IyMEEYwgc', 'doxcnmoP7vP7KFmcxl5D47v8jzh', 'doxcn0ZjKX7XgcM681soN1OAtHf', 'doxcnQtAN3qglnWfmjVrxH6e6Dg'], 'parent_id': 'doxcnYyvLoFOf6Oi5fBfJMPi0bb'}, {'block_id': 'doxcne7gH06jH0shKDBxzDwmaNg', 'block_type': 2, 'parent_id': 'doxcn12YdrBTNqEVMP31nJD7uQc', 'text': {'elements': [{'text_run': {'content': '总结概述', 'text_element_style': {'bold': True, 'inline_code': False, 'italic': False, 'strikethrough': False, 'underline': False}}}], 'style': {'align': 1, 'folded': False}}}, {'block_id': 'doxcnPzf5FWoYgX397UQ5bGz6GO', 'block_type': 12, 'bullet': {'elements': [{'text_run': {'content': '非一线占比', 'text_element_style': {'bold': True, 'inline_code': False, 'italic': False, 'strikethrough': False, 'underline': False}}}, {'text_run': {'content': '：CQC（不含DMC）2月底非一线占比看板1，；', 'text_element_style': {'bold': False, 'inline_code': False, 'italic': False, 'strikethrough': False, 'underline': False}}}, {'text_run': {'content': '【数据来源于表4】', 'text_element_style': {'background_color': 14, 'bold': False, 'inline_code': False, 'italic': False, 'strikethrough': False, 'underline': False}}}], 'style': {'align': 1, 'folded': False}}, 'children': ['doxcndyVWKFwUmZWAbEWaR6ePMf'], 'parent_id': 'doxcn12YdrBTNqEVMP31nJD7uQc'}, {'block_id': 'doxcndyVWKFwUmZWAbEWaR6ePMf', 'block_type': 12, 'bullet': {'elements': [{'text_run': {'content': ' 非一线占比情况：商业安全-合计 > 商安-电商 > 头条群组 > 运营支持中心 > 直播群组 > 商安-生活服务 > 抖音群组 > 垂直群组 > 商安-广告经营', 'text_element_style': {'bold': True, 'inline_code': False, 'italic': False, 'strikethrough': False, 'underline': False}}}], 'style': {'align': 1, 'folded': False}}, 'parent_id': 'doxcnPzf5FWoYgX397UQ5bGz6GO'}, {'block_id': 'doxcn6H4R2acBzc8jPo4WYwrXDe', 'block_type': 12, 'bullet': {'elements': [{'text_run': {'content': '提速率：', 'text_element_style': {'bold': True, 'inline_code': False, 'italic': False, 'strikethrough': False, 'underline': False}}}, {'text_run': {'content': 'CQC（不含DMC）季度累计提速1.23%，较Q1目标（-1.4%）总结向坏;', 'text_element_style': {'bold': False, 'inline_code': False, 'italic': False, 'strikethrough': False, 'underline': False}}}, {'text_run': {'content': '【数据来源于表4】', 'text_element_style': {'background_color': 14, 'bold': False, 'inline_code': False, 'italic': False, 'strikethrough': False, 'underline': False}}}], 'style': {'align': 1, 'folded': False}}, 'children': ['doxcnWlNhYMVuC69TLk2FTvbApd'], 'parent_id': 'doxcn12YdrBTNqEVMP31nJD7uQc'}, {'block_id': 'doxcnWlNhYMVuC69TLk2FTvbApd', 'block_type': 12, 'bullet': {'elements': [{'text_run': {'content': '提速率情况：商安-电商 > 垂直群组 > 商业安全-合计 > 商安-生活服务 > 商安-广告经营 > 直播群组 > 运营支持中心 > 抖音群组 > 头条群组', 'text_element_style': {'bold': True, 'inline_code': False, 'italic': False, 'strikethrough': False, 'underline': False}}}], 'style': {'align': 1, 'folded': False}}, 'parent_id': 'doxcn6H4R2acBzc8jPo4WYwrXDe'}, {'block_id': 'doxcnuITtBD0V0qPs4IyMEEYwgc', 'block_type': 12, 'bullet': {'elements': [{'text_run': {'content': '审核时长占比：', 'text_element_style': {'bold': True, 'inline_code': False, 'italic': False, 'strikethrough': False, 'underline': False}}}, {'text_run': {'content': 'CQC（不含DMC）2月审核时长占比环比12月--,；', 'text_element_style': {'bold': False, 'inline_code': False, 'italic': False, 'strikethrough': False, 'underline': False}}}, {'text_run': {'content': '【数据来源于表4】', 'text_element_style': {'background_color': 14, 'bold': False, 'inline_code': False, 'italic': False, 'strikethrough': False, 'underline': False}}}], 'style': {'align': 1, 'folded': False}}, 'children': ['doxcnH2EZZj6YNYPFKWgALSsdEe'], 'parent_id': 'doxcn12YdrBTNqEVMP31nJD7uQc'}, {'block_id': 'doxcnH2EZZj6YNYPFKWgALSsdEe', 'block_type': 12, 'bullet': {'elements': [{'text_run': {'content': '审核时长占比情况：商安-生活服务 > 商安-广告经营 > 商业安全-合计 > 运营支持中心 > 商安-电商 > 抖音群组 > 头条群组 > 直播群组 > 垂直群组', 'text_element_style': {'bold': True, 'inline_code': False, 'italic': False, 'strikethrough': False, 'underline': False}}}], 'style': {'align': 1, 'folded': False}}, 'parent_id': 'doxcnuITtBD0V0qPs4IyMEEYwgc'}, {'block_id': 'doxcnmoP7vP7KFmcxl5D47v8jzh', 'block_type': 2, 'parent_id': 'doxcn12YdrBTNqEVMP31nJD7uQc', 'text': {'elements': [{'text_run': {'content': 'Highlight', 'text_element_style': {'background_color': 4, 'bold': True, 'inline_code': False, 'italic': False, 'strikethrough': False, 'text_color': 4, 'underline': False}}}], 'style': {'align': 1, 'folded': False}}}, {'block_id': 'doxcn0ZjKX7XgcM681soN1OAtHf', 'block_type': 12, 'bullet': {'elements': [{'text_run': {'content': '提速率：', 'text_element_style': {'bold': True, 'inline_code': False, 'italic': False, 'strikethrough': False, 'underline': False}}}, {'text_run': {'content': '各', 'text_element_style': {'bold': False, 'inline_code': False, 'italic': False, 'strikethrough': False, 'underline': False}}}, {'text_run': {'content': '组织提速策略落地效果显著，抖音群组、头条群组、运营支持中心未达成目标；', 'text_element_style': {'bold': True, 'inline_code': False, 'italic': False, 'strikethrough': False, 'underline': False}}}, {'text_run': {'content': '【数据来源于表4】', 'text_element_style': {'background_color': 14, 'bold': True, 'inline_code': False, 'italic': False, 'strikethrough': False, 'underline': False}}}], 'style': {'align': 1, 'folded': False}}, 'children': ['doxcn1dVKnZmW76kaUigVqPRoGb', 'doxcntFkbTuaHm5aG9peMiUbvBe', 'doxcn2QBmAKuzUgm3WEKLfAhDUb'], 'parent_id': 'doxcn12YdrBTNqEVMP31nJD7uQc'}, {'block_id': 'doxcn1dVKnZmW76kaUigVqPRoGb', 'block_type': 12, 'bullet': {'elements': [{'text_run': {'content': 'DMC：{\"RAG\":\"提速率高的原因\"}；', 'text_element_style': {'bold': True, 'inline_code': False, 'italic': False, 'strikethrough': False, 'underline': False}}}], 'style': {'align': 1, 'folded': False}}, 'parent_id': 'doxcn0ZjKX7XgcM681soN1OAtHf'}, {'block_id': 'doxcntFkbTuaHm5aG9peMiUbvBe', 'block_type': 12, 'bullet': {'elements': [{'text_run': {'content': '商安-电商：{\"RAG\":\"提速率高的原因\"}；', 'text_element_style': {'bold': True, 'inline_code': False, 'italic': False, 'strikethrough': False, 'underline': False}}}], 'style': {'align': 1, 'folded': False}}, 'parent_id': 'doxcn0ZjKX7XgcM681soN1OAtHf'}, {'block_id': 'doxcn2QBmAKuzUgm3WEKLfAhDUb', 'block_type': 12, 'bullet': {'elements': [{'text_run': {'content': '垂直群组：{\"RAG\":\"提速率高的原因\"}；', 'text_element_style': {'bold': True, 'inline_code': False, 'italic': False, 'strikethrough': False, 'underline': False}}}], 'style': {'align': 1, 'folded': False}}, 'parent_id': 'doxcn0ZjKX7XgcM681soN1OAtHf'}, {'block_id': 'doxcnQtAN3qglnWfmjVrxH6e6Dg', 'block_type': 2, 'children': ['doxcn26sP2hFETuu9EloILQWtoe'], 'parent_id': 'doxcn12YdrBTNqEVMP31nJD7uQc', 'text': {'elements': [{'text_run': {'content': '关注点', 'text_element_style': {'background_color': 2, 'bold': True, 'inline_code': False, 'italic': False, 'strikethrough': False, 'text_color': 2, 'underline': False}}}], 'style': {'align': 1, 'folded': False}}}, {'block_id': 'doxcn26sP2hFETuu9EloILQWtoe', 'block_type': 12, 'bullet': {'elements': [{'text_run': {'content': '{\"RAG\":\"提速率不达标群组以及原因\"}', 'text_element_style': {'bold': True, 'inline_code': False, 'italic': False, 'strikethrough': False, 'underline': False}}}], 'style': {'align': 1, 'folded': False}}, 'parent_id': 'doxcnQtAN3qglnWfmjVrxH6e6Dg'}, {'block_id': 'doxcnaFFm4cgJBRfYspOUdxMFDd', 'block_type': 30, 'parent_id': 'doxcnYyvLoFOf6Oi5fBfJMPi0bb', 'sheet': {'token': 'D9XismEq0hOGoXttIOlcj4ponWe_XzbCyl'}}, {'block_id': 'doxcnpsa9rYQWyd6PjjNRCw8zsd', 'block_type': 5, 'children': ['doxcnsIFTKIqdH36A0FVPUvtA7e', 'doxcn0S59P9814WKwg9S2b93TZg'], 'heading3': {'elements': [{'text_run': {'content': '三、 人力变动明细及关键举措情况', 'text_element_style': {'bold': False, 'inline_code': False, 'italic': False, 'strikethrough': False, 'underline': False}}}], 'style': {'align': 1, 'folded': False}}, 'parent_id': 'W8XYdLK9JoITNRxYBvKcmwHvnRR'}, {'block_id': 'doxcnsIFTKIqdH36A0FVPUvtA7e', 'block_type': 19, 'callout': {'border_color': 5, 'emoji_id': 'lower_left_crayon'}, 'children': ['doxcnzn7kogoo212EyJlaUX10Zd', 'doxcnKHisTt0EAY5ufbPuhRABUh', 'doxcnZngiI5qJfz5ir6Synibpoe', 'doxcnLPdqaizO7b5XRdMQOlVweb'], 'parent_id': 'doxcnpsa9rYQWyd6PjjNRCw8zsd'}, {'block_id': 'doxcnzn7kogoo212EyJlaUX10Zd', 'block_type': 2, 'parent_id': 'doxcnsIFTKIqdH36A0FVPUvtA7e', 'text': {'elements': [{'text_run': {'content': '总结概述：', 'text_element_style': {'bold': True, 'inline_code': False, 'italic': False, 'strikethrough': False, 'underline': False}}}, {'text_run': {'content': '主动举措影响（管理动作HC优化）0人', 'text_element_style': {'bold': False, 'inline_code': False, 'italic': False, 'strikethrough': False, 'underline': False}}}, {'text_run': {'content': '，0.0%', 'text_element_style': {'bold': True, 'inline_code': False, 'italic': False, 'strikethrough': False, 'underline': False}}}, {'text_run': {'content': '，从各组织管理动作收益占比来看：抖音群组 > 直播群组 > 头条群组 > 垂直群组 > 广告 > 生活服务 > 电商 > 运营支持中心', 'text_element_style': {'bold': False, 'inline_code': False, 'italic': False, 'strikethrough': False, 'underline': False}}}, {'text_run': {'content': '  ', 'text_element_style': {'bold': True, 'inline_code': False, 'italic': False, 'strikethrough': False, 'underline': False}}}, {'text_run': {'content': '【数据来源于表4】', 'text_element_style': {'background_color': 14, 'bold': True, 'inline_code': False, 'italic': False, 'strikethrough': False, 'underline': False}}}], 'style': {'align': 1, 'folded': False}}}, {'block_id': 'doxcnKHisTt0EAY5ufbPuhRABUh', 'block_type': 12, 'bullet': {'elements': [{'text_run': {'content': '主动降量', 'text_element_style': {'bold': True, 'inline_code': False, 'italic': False, 'strikethrough': False, 'underline': False}}}, {'text_run': {'content': '（#VALUE!人）', 'text_element_style': {'bold': False, 'inline_code': False, 'italic': True, 'strikethrough': False, 'underline': False}}}, {'text_run': {'content': '：其中主要为抖音群组 群组月报', 'text_element_style': {'bold': False, 'inline_code': False, 'italic': False, 'strikethrough': False, 'underline': False}}}, {'text_run': {'content': '人', 'text_element_style': {'bold': True, 'inline_code': False, 'italic': False, 'strikethrough': False, 'underline': False}}}, {'text_run': {'content': '（{\"RAG\":\"主动降量原因\"}），直播群组 {\\'表格5\\':\\'A5:I14\\',\\'G\\',\\'G\\'1,\\'A#群组|商安-|中心|DMC\\',1} 人（{\"RAG\":\"主动降量原因\"}）），头条群组 群组月报 ', 'text_element_style': {'bold': False, 'inline_code': False, 'italic': False, 'strikethrough': False, 'underline': False}}}, {'text_run': {'content': '人', 'text_element_style': {'bold': True, 'inline_code': False, 'italic': False, 'strikethrough': False, 'underline': False}}}, {'text_run': {'content': '（{\"RAG\":\"主动降量原因\"}））；', 'text_element_style': {'bold': False, 'inline_code': False, 'italic': False, 'strikethrough': False, 'underline': False}}}], 'style': {'align': 1, 'folded': False}}, 'parent_id': 'doxcnsIFTKIqdH36A0FVPUvtA7e'}, {'block_id': 'doxcnZngiI5qJfz5ir6Synibpoe', 'block_type': 12, 'bullet': {'elements': [{'text_run': {'content': '人效变化', 'text_element_style': {'bold': True, 'inline_code': False, 'italic': False, 'strikethrough': False, 'underline': False}}}, {'text_run': {'content': '（#VALUE!人）', 'text_element_style': {'bold': False, 'inline_code': False, 'italic': True, 'strikethrough': False, 'underline': False}}}, {'text_run': {'content': '：其中主要为抖音群组 群组月报 ', 'text_element_style': {'bold': False, 'inline_code': False, 'italic': False, 'strikethrough': False, 'underline': False}}}, {'text_run': {'content': '人', 'text_element_style': {'bold': True, 'inline_code': False, 'italic': False, 'strikethrough': False, 'underline': False}}}, {'text_run': {'content': '（{\"RAG\":\"人效变化原因\"}）），直播群组 群组月报 ', 'text_element_style': {'bold': False, 'inline_code': False, 'italic': False, 'strikethrough': False, 'underline': False}}}, {'text_run': {'content': '人', 'text_element_style': {'bold': True, 'inline_code': False, 'italic': False, 'strikethrough': False, 'underline': False}}}, {'text_run': {'content': '（{\"RAG\":\"主动降量原因\"}）），头条群组 群组月报', 'text_element_style': {'bold': False, 'inline_code': False, 'italic': False, 'strikethrough': False, 'underline': False}}}, {'text_run': {'content': '人', 'text_element_style': {'bold': True, 'inline_code': False, 'italic': False, 'strikethrough': False, 'underline': False}}}, {'text_run': {'content': '（{\"RAG\":\"主动降量原因\"}））；', 'text_element_style': {'bold': False, 'inline_code': False, 'italic': False, 'strikethrough': False, 'underline': False}}}], 'style': {'align': 1, 'folded': False}}, 'parent_id': 'doxcnsIFTKIqdH36A0FVPUvtA7e'}, {'block_id': 'doxcnLPdqaizO7b5XRdMQOlVweb', 'block_type': 12, 'bullet': {'elements': [{'text_run': {'content': '非一线优化', 'text_element_style': {'bold': True, 'inline_code': False, 'italic': False, 'strikethrough': False, 'underline': False}}}, {'text_run': {'content': '（#VALUE!人）：其中主要为抖音群组 群组月报', 'text_element_style': {'bold': False, 'inline_code': False, 'italic': False, 'strikethrough': False, 'underline': False}}}, {'text_run': {'content': '人', 'text_element_style': {'bold': True, 'inline_code': False, 'italic': False, 'strikethrough': False, 'underline': False}}}, {'text_run': {'content': '（{\"RAG\":\"非一线优化原因\"}）），直播群组 群组月报', 'text_element_style': {'bold': False, 'inline_code': False, 'italic': False, 'strikethrough': False, 'underline': False}}}, {'text_run': {'content': '人', 'text_element_style': {'bold': True, 'inline_code': False, 'italic': False, 'strikethrough': False, 'underline': False}}}, {'text_run': {'content': '（{\"RAG\":\"非一线优化原因\"}）），头条群组 群组月报 ', 'text_element_style': {'bold': False, 'inline_code': False, 'italic': False, 'strikethrough': False, 'underline': False}}}, {'text_run': {'content': '人', 'text_element_style': {'bold': True, 'inline_code': False, 'italic': False, 'strikethrough': False, 'underline': False}}}, {'text_run': {'content': '（{\"RAG\":\"非一线优化原因\"}））；；', 'text_element_style': {'bold': False, 'inline_code': False, 'italic': False, 'strikethrough': False, 'underline': False}}}], 'style': {'align': 1, 'folded': False}}, 'parent_id': 'doxcnsIFTKIqdH36A0FVPUvtA7e'}, {'block_id': 'doxcn0S59P9814WKwg9S2b93TZg', 'block_type': 30, 'parent_id': 'doxcnpsa9rYQWyd6PjjNRCw8zsd', 'sheet': {'token': 'D9XismEq0hOGoXttIOlcj4ponWe_4cUi91'}}, {'block_id': 'doxcn5hmFVoWktM3V6Fnmhe0JQe', 'block_type': 5, 'children': ['doxcnWZx0UTne4jYTuLaHpw0ONc', 'doxcnZgY2tqwwON9A2EyiAroztf'], 'heading3': {'elements': [{'text_run': {'content': '四、 消费环节关键指标情况', 'text_element_style': {'bold': False, 'inline_code': False, 'italic': False, 'strikethrough': False, 'underline': False}}}], 'style': {'align': 1, 'folded': False}}, 'parent_id': 'W8XYdLK9JoITNRxYBvKcmwHvnRR'}, {'block_id': 'doxcnWZx0UTne4jYTuLaHpw0ONc', 'block_type': 19, 'callout': {'border_color': 5, 'emoji_id': 'writing_hand'}, 'children': ['doxcnYhGChrmHgmesqxxjbFxQjd', 'doxcnST60wGWLWPTFgQR65jcTlf', 'doxcnZNfpuXZH3pHLka7DMPrdxd'], 'parent_id': 'doxcn5hmFVoWktM3V6Fnmhe0JQe'}, {'block_id': 'doxcnYhGChrmHgmesqxxjbFxQjd', 'block_type': 2, 'parent_id': 'doxcnWZx0UTne4jYTuLaHpw0ONc', 'text': {'elements': [{'text_run': {'content': '总', 'text_element_style': {'bold': True, 'inline_code': False, 'italic': False, 'strikethrough': False, 'underline': False}}}, {'text_run': {'content': '结概述：', 'text_element_style': {'bold': True, 'inline_code': False, 'italic': False, 'strikethrough': False, 'underline': False}}}], 'style': {'align': 1, 'folded': False}}}, {'block_id': 'doxcnST60wGWLWPTFgQR65jcTlf', 'block_type': 12, 'bullet': {'elements': [{'text_run': {'content': 'CQC合计Q1和各上游对齐季度关键指标（不含效率类）合计#VALUE!个，截止2月底，暂未达成个数#VALUE!个(未达成率#VALUE!)，拆分群组来看；', 'text_element_style': {'bold': True, 'inline_code': False, 'italic': False, 'strikethrough': False, 'underline': False}}}], 'style': {'align': 1, 'folded': False}}, 'children': ['doxcnOH1HBL5NJCdQAeEp2xl1lh', 'doxcnlDe1ukwcP344uPtgoX1x0d', 'doxcnxqJMSwX4e7e5vtpS8DrJSf', 'doxcnVW9Fipneun7x8QpNFvgHSc', 'doxcndL6kWkS5Su7bp3Du5SLPEc', 'doxcnAwtlSVtGy6wiB9f2C1Iujy', 'doxcnsoAOwIwkuYnwxFitLGx23e', 'doxcnzoZXoZOa5soV7CEW9ys1df'], 'parent_id': 'doxcnWZx0UTne4jYTuLaHpw0ONc'}, {'block_id': 'doxcnOH1HBL5NJCdQAeEp2xl1lh', 'block_type': 12, 'bullet': {'elements': [{'text_run': {'content': '抖音群组（未达标数基于群组月报汇总/总量基于群组月报汇总）；', 'text_element_style': {'bold': False, 'inline_code': False, 'italic': False, 'strikethrough': False, 'underline': False}}}], 'style': {'align': 1, 'folded': False}}, 'parent_id': 'doxcnST60wGWLWPTFgQR65jcTlf'}, {'block_id': 'doxcnlDe1ukwcP344uPtgoX1x0d', 'block_type': 12, 'bullet': {'elements': [{'text_run': {'content': '直播群组（未达标数基于群组月报汇总/总量基于群组月报汇总）；', 'text_element_style': {'bold': False, 'inline_code': False, 'italic': False, 'strikethrough': False, 'underline': False}}}], 'style': {'align': 1, 'folded': False}}, 'parent_id': 'doxcnST60wGWLWPTFgQR65jcTlf'}, {'block_id': 'doxcnxqJMSwX4e7e5vtpS8DrJSf', 'block_type': 12, 'bullet': {'elements': [{'text_run': {'content': '头条群组（未达标数基于群组月报汇总/总量基于群组月报汇总）；', 'text_element_style': {'bold': False, 'inline_code': False, 'italic': False, 'strikethrough': False, 'underline': False}}}], 'style': {'align': 1, 'folded': False}}, 'parent_id': 'doxcnST60wGWLWPTFgQR65jcTlf'}, {'block_id': 'doxcnVW9Fipneun7x8QpNFvgHSc', 'block_type': 12, 'bullet': {'elements': [{'text_run': {'content': '垂直群组（未达标数基于群组月报汇总/总量基于群组月报汇总）；', 'text_element_style': {'bold': False, 'inline_code': False, 'italic': False, 'strikethrough': False, 'underline': False}}}], 'style': {'align': 1, 'folded': False}}, 'parent_id': 'doxcnST60wGWLWPTFgQR65jcTlf'}, {'block_id': 'doxcndL6kWkS5Su7bp3Du5SLPEc', 'block_type': 12, 'bullet': {'elements': [{'text_run': {'content': '广告（未达标数基于群组月报汇总/总量基于群组月报汇总）；', 'text_element_style': {'bold': False, 'inline_code': False, 'italic': False, 'strikethrough': False, 'underline': False}}}], 'style': {'align': 1, 'folded': False}}, 'parent_id': 'doxcnST60wGWLWPTFgQR65jcTlf'}, {'block_id': 'doxcnAwtlSVtGy6wiB9f2C1Iujy', 'block_type': 12, 'bullet': {'elements': [{'text_run': {'content': '生活服务（未达标数基于群组月报汇总/总量基于群组月报汇总）；', 'text_element_style': {'bold': False, 'inline_code': False, 'italic': False, 'strikethrough': False, 'underline': False}}}], 'style': {'align': 1, 'folded': False}}, 'parent_id': 'doxcnST60wGWLWPTFgQR65jcTlf'}, {'block_id': 'doxcnsoAOwIwkuYnwxFitLGx23e', 'block_type': 12, 'bullet': {'elements': [{'text_run': {'content': '电商（未达标数基于群组月报汇总/总量基于群组月报汇总）', 'text_element_style': {'bold': False, 'inline_code': False, 'italic': False, 'strikethrough': False, 'underline': False}}}], 'style': {'align': 1, 'folded': False}}, 'parent_id': 'doxcnST60wGWLWPTFgQR65jcTlf'}, {'block_id': 'doxcnzoZXoZOa5soV7CEW9ys1df', 'block_type': 12, 'bullet': {'elements': [{'text_run': {'content': '运营支持中心（未达标数基于群组月报汇总/总量基于群组月报汇总）；', 'text_element_style': {'bold': False, 'inline_code': False, 'italic': False, 'strikethrough': False, 'underline': False}}}], 'style': {'align': 1, 'folded': False}}, 'parent_id': 'doxcnST60wGWLWPTFgQR65jcTlf'}, {'block_id': 'doxcnZNfpuXZH3pHLka7DMPrdxd', 'block_type': 2, 'parent_id': 'doxcnWZx0UTne4jYTuLaHpw0ONc', 'text': {'elements': [{'text_run': {'content': '附：', 'text_element_style': {'bold': True, 'inline_code': False, 'italic': False, 'strikethrough': False, 'underline': False}}}, {'mention_doc': {'obj_type': 3, 'text_element_style': {'bold': True, 'inline_code': False, 'italic': False, 'strikethrough': False, 'underline': False}, 'title': '【消费环节】交付上游北极星指标情况', 'token': 'OSXDsmnFyhW4fatUE9ecY1DQnUg', 'url': 'https://bytedance.larkoffice.com/sheets/OSXDsmnFyhW4fatUE9ecY1DQnUg'}}, {'text_run': {'content': '，暂未达成明细详见附录1', 'text_element_style': {'bold': True, 'inline_code': False, 'italic': False, 'strikethrough': False, 'underline': False}}}], 'style': {'align': 1, 'folded': False}}}, {'block_id': 'doxcnZgY2tqwwON9A2EyiAroztf', 'block_type': 30, 'parent_id': 'doxcn5hmFVoWktM3V6Fnmhe0JQe', 'sheet': {'token': 'D9XismEq0hOGoXttIOlcj4ponWe_pjZ4T7'}}, {'block_id': 'doxcnlVFWLckabAoHP8KnBCIKFg', 'block_type': 6, 'children': ['doxcn0YaaxU75txhMvA6GJ9Jlsh'], 'heading4': {'elements': [{'text_run': {'content': '消费北极星指标未达成的情况', 'text_element_style': {'bold': False, 'inline_code': False, 'italic': False, 'strikethrough': False, 'underline': False}}}, {'text_run': {'content': '【基于群组月报汇总】', 'text_element_style': {'background_color': 14, 'bold': False, 'inline_code': False, 'italic': False, 'strikethrough': False, 'underline': False}}}], 'style': {'align': 1, 'folded': False}}, 'parent_id': 'W8XYdLK9JoITNRxYBvKcmwHvnRR'}, {'block_id': 'doxcn0YaaxU75txhMvA6GJ9Jlsh', 'block_type': 30, 'parent_id': 'doxcnlVFWLckabAoHP8KnBCIKFg', 'sheet': {'token': 'D9XismEq0hOGoXttIOlcj4ponWe_wycq9B'}}, {'block_id': 'doxcnCYw6gDFLQjlnnLH5zSSTEh', 'block_type': 2, 'parent_id': 'W8XYdLK9JoITNRxYBvKcmwHvnRR', 'text': {'elements': [{'text_run': {'content': '', 'text_element_style': {'bold': False, 'inline_code': False, 'italic': False, 'strikethrough': False, 'underline': False}}}], 'style': {'align': 1, 'folded': False}}}, {'block_id': 'doxcnkK8ZsI76ie1QuZSgMl6HCf', 'block_type': 6, 'children': ['doxcn2E76OyGyFO0puGdNn1GoBc'], 'heading4': {'elements': [{'text_run': {'content': '附录1：各群组月报 （2月）', 'text_element_style': {'bold': False, 'inline_code': False, 'italic': False, 'strikethrough': False, 'underline': False}}}], 'style': {'align': 1, 'folded': False}}, 'parent_id': 'W8XYdLK9JoITNRxYBvKcmwHvnRR'}, {'block_id': 'doxcn2E76OyGyFO0puGdNn1GoBc', 'block_type': 30, 'parent_id': 'doxcnkK8ZsI76ie1QuZSgMl6HCf', 'sheet': {'token': 'D9XismEq0hOGoXttIOlcj4ponWe_cQH4n6'}}, {'block_id': 'doxcniFITmqYuod8lFal89Q0Dih', 'block_type': 2, 'parent_id': 'W8XYdLK9JoITNRxYBvKcmwHvnRR', 'text': {'elements': [{'text_run': {'content': '', 'text_element_style': {'bold': False, 'inline_code': False, 'italic': False, 'strikethrough': False, 'underline': False}}}], 'style': {'align': 1, 'folded': False}}}], 'success': True}\n"]}], "source": ["import requests\n", "\n", "# url = \"http://127.0.0.1:5000/api/feishu/doc_text_modify\"\n", "url = \"http://cqctool-cbao.bytedance.net/api/feishu/doc_text_modify\"\n", "\n", "payload = {\n", "    \"user_id\":\"test_user_001\",\n", "    \"doc_id\":\"W8XYdLK9JoITNRxYBvKcmwHvnRR\",\n", "    \"month\":\"2月\",\n", "    \"user_token\":\"u-gkANeDdrx2AFkDL1EHo1yU5h6Ozg50Ejrq20khy82dyR\"\n", "}\n", "headers = {\"Content-Type\": \"application/json\"}\n", "\n", "response = requests.post(url, json=payload, headers=headers)\n", "print(response.status_code)\n", "print(response.json())"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "base", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.2"}}, "nbformat": 4, "nbformat_minor": 2}