{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# 常量"]}, {"cell_type": "code", "execution_count": 53, "metadata": {}, "outputs": [], "source": ["USER_TOEKN=\"**********************************************\"\n", "USER_ID = \"test_user_001\"\n", "doc_url = 'https://bytedance.larkoffice.com/docx/KnSBdQNANozPHkxRAObcP6R6nIg#share-SmoZd6so4oI5XLx8G4OcWgiynWe'"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# 用户注册"]}, {"cell_type": "code", "execution_count": 54, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["状态码: 200\n", "响应内容: {\n", "  \"message\": \"用户注册成功\",\n", "  \"success\": true,\n", "  \"user_id\": \"test_user_001\"\n", "}\n"]}], "source": ["import requests\n", "import json\n", "\n", "# 正确的接口URL\n", "url = \"http://127.0.0.1:5000/api/register\"\n", "url = \"https://cqctool-cbao.bytedance.net/api/register\"\n", "# 请求数据\n", "payload = {\n", "    \"user_id\": USER_ID,\n", "    \"user_token\": USER_TOEKN\n", "}\n", "\n", "# 发送POST请求\n", "headers = {\"Content-Type\": \"application/json\"}\n", "response = requests.post(url, json=payload, headers=headers)\n", "\n", "# 打印响应结果\n", "print(f\"状态码: {response.status_code}\")\n", "try:\n", "    print(f\"响应内容: {json.dumps(response.json(), ensure_ascii=False, indent=2)}\")\n", "except:\n", "    print(f\"响应内容: {response.text}\")"]}, {"cell_type": "code", "execution_count": 55, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["200\n", "{'group_report_status': True, 'message': '完成群组月报配置检查', 'user_id': 'test_user_001'}\n"]}], "source": ["# 群组月报检查\n", "import requests\n", "\n", "url = \"http://cqctool-cbao.bytedance.net/api/feishu/group_report_check\"\n", "# url = \"http://127.0.0.1:5000/api/feishu/group_report_check\"\n", "payload = {\n", "    \"user_id\":USER_ID\n", "}\n", "headers = {\"Content-Type\": \"application/json\"}\n", "\n", "response = requests.post(url, json=payload, headers=headers)\n", "print(response.status_code)\n", "print(response.json())"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# 群组月报保存"]}, {"cell_type": "code", "execution_count": 56, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["状态码: 504\n", "响应内容: Gateway Timeout\n"]}], "source": ["import requests\n", "import json\n", "\n", "# 正确的接口URL\n", "url = \"http://127.0.0.1:5000/api/feishu/group_report_file\"\n", "url = \"http://cqctool-cbao.bytedance.net/api/feishu/group_report_file\"\n", "# 请求数据\n", "payload = {\n", "    \"user_id\": USER_ID,\n", "    \"user_token\": USER_TOEKN\n", "}\n", "\n", "# 发送POST请求\n", "headers = {\"Content-Type\": \"application/json\"}\n", "response = requests.post(url, json=payload, headers=headers)\n", "\n", "# 打印响应结果\n", "print(f\"状态码: {response.status_code}\")\n", "try:\n", "    print(f\"响应内容: {json.dumps(response.json(), ensure_ascii=False, indent=2)}\")\n", "except:\n", "    print(f\"响应内容: {response.text}\")"]}, {"cell_type": "code", "execution_count": 5, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["{'error': \"读取飞书文档失败: cannot access local variable 'idx' where it is not associated with a value\"}\n"]}, {"ename": "KeyError", "evalue": "'block_id'", "output_type": "error", "traceback": ["\u001b[0;31m---------------------------------------------------------------------------\u001b[0m", "\u001b[0;31m<PERSON><PERSON><PERSON><PERSON><PERSON>\u001b[0m                                  <PERSON><PERSON> (most recent call last)", "Cell \u001b[0;32mIn[5], line 16\u001b[0m\n\u001b[1;32m     14\u001b[0m js \u001b[38;5;241m=\u001b[39m response\u001b[38;5;241m.\u001b[39mjson()\n\u001b[1;32m     15\u001b[0m \u001b[38;5;28mprint\u001b[39m(js)\n\u001b[0;32m---> 16\u001b[0m \u001b[38;5;28;01mif\u001b[39;00m \u001b[38;5;129;01mnot\u001b[39;00m js[\u001b[38;5;124m'\u001b[39m\u001b[38;5;124mblock_id\u001b[39m\u001b[38;5;124m'\u001b[39m]:\n\u001b[1;32m     17\u001b[0m     \u001b[38;5;28;01<PERSON><PERSON>\u001b[39;00m\n", "\u001b[0;31mKeyError\u001b[0m: 'block_id'"]}], "source": ["import requests\n", "\n", "block_id = None\n", "# url = \"http://cqctool-cbao.bytedance.net/api/feishu/get_sheet_block\"\n", "url = \"http://127.0.0.1:5000/api/feishu/get_sheet_block\"\n", "payload = {\n", "    \"user_id\":USER_ID,\n", "    \"url\": doc_url,  # 替换为实际飞书文档链接\n", "    \"user_token\": USER_TOEKN  # 如无需用户token可去掉此行或设为None\n", "}\n", "headers = {\"Content-Type\": \"application/json\"}\n", "while True:\n", "    response = requests.post(url, json=payload, headers=headers)\n", "    js = response.json()\n", "    print(js)\n", "    if not js['block_id']:\n", "        break"]}, {"cell_type": "code", "execution_count": 15, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["{'block_id': 'doxcne05P9IjYtTLCqECRIn3ige', 'doc_id': 'YIvmdTalpoU0sHxIjdMc5aLknRh', 'message': '开始第1个表格数据写入....', 'sheet_token': 'FLp2sEDUshX928t41flcWdJZnve_n83h91', 'user_id': 'test_user_001'}\n", "doxcne05P9IjYtTLCqECRIn3ige FLp2sEDUshX928t41flcWdJZnve_n83h91 FLp2sEDUshX928t41flcWdJZnve n83h91\n", "{'code': '0', 'data': {'_flowExecutionID': 1831461828517987}, 'message': [{'language_code': 1033, 'text': 'Success'}, {'language_code': 2052, 'text': '成功'}], 'status': 'success', 'success': True}\n", "200\n", "{'message': '表格写入成功', 'success': True}\n"]}], "source": ["# 返回各个表格的token\n", "import requests\n", "\n", "block_id = None\n", "# url = \"http://cqctool-cbao.bytedance.net/api/feishu/get_sheet_block\"\n", "url = \"http://127.0.0.1:5000/api/feishu/get_sheet_block\"\n", "payload = {\n", "    \"user_id\":USER_ID,\n", "    \"url\": doc_url,  # 替换为实际飞书文档链接\n", "    \"user_token\": USER_TOEKN  # 如无需用户token可去掉此行或设为None\n", "}\n", "headers = {\"Content-Type\": \"application/json\"}\n", "while True:\n", "    response = requests.post(url, json=payload, headers=headers)\n", "    js = response.json()\n", "    print(js)\n", "    if not js['block_id']:\n", "        break\n", "\n", "    block_id = js['block_id']\n", "    sheet_token = js['sheet_token']\n", "    spreadsheet_token = sheet_token.strip().split('_')[0]\n", "    sheet_id = sheet_token.strip().split('_')[1]\n", "    print(block_id, sheet_token, spreadsheet_token, sheet_id)\n", "\n", "    # 填写配置的数据\n", "    url_='http://127.0.0.1:5000/api/report_etl/trigger_batch_insert'\n", "    payload_ = {\n", "        \"spreadsheet_token\": spreadsheet_token,\n", "        \"sheet_id\": sheet_id\n", "    }\n", "    response = requests.post(url_, headers=headers,json=payload_)\n", "    print(response.json())\n", "\n", "    # 填写rag数据\n", "    url_ = \"http://127.0.0.1:5000/api/rag2sheet\"\n", "\n", "    payload_ = {\n", "        \"user_id\":USER_ID,\n", "        \"sheet_token\":sheet_token,\n", "        \"user_token\":USER_TOEKN\n", "    }\n", "\n", "    response = requests.post(url_, json=payload_, headers=headers)\n", "    print(response.status_code)\n", "    print(response.json())\n", "\n", "    break \n", "\n", "\n", "\n"]}, {"cell_type": "code", "execution_count": 6, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["doxcnWPd77Y78wPqiDComdmugQb FLp2sEDUshX928t41flcWdJZnve_uOf1wz FLp2sEDUshX928t41flcWdJZnve uOf1wz\n", "{'code': '0', 'data': {'_flowExecutionID': 1831426644342824}, 'message': [{'language_code': 1033, 'text': 'Success'}, {'language_code': 2052, 'text': '成功'}], 'status': 'success', 'success': True}\n", "200\n", "{'message': '无需写入', 'success': True}\n"]}], "source": ["# 返回各个表格的token\n", "import requests\n", "\n", "block_id = None\n", "# url = \"http://cqctool-cbao.bytedance.net/api/feishu/get_sheet_block\"\n", "url = \"http://127.0.0.1:5000/api/feishu/get_sheet_block\"\n", "payload = {\n", "    \"user_id\":USER_ID,\n", "    \"url\": doc_url,  # 替换为实际飞书文档链接\n", "    \"user_token\": USER_TOEKN  # 如无需用户token可去掉此行或设为None\n", "}\n", "headers = {\"Content-Type\": \"application/json\"}\n", "while True:\n", "    response = requests.post(url, json=payload, headers=headers)\n", "    js = response.json()\n", "    if not js['block_id']:\n", "        break\n", "\n", "    block_id = js['block_id']\n", "    sheet_token = js['sheet_token']\n", "    spreadsheet_token = sheet_token.strip().split('_')[0]\n", "    sheet_id = sheet_token.strip().split('_')[1]\n", "    print(block_id, sheet_token, spreadsheet_token, sheet_id)\n", "\n", "    # 填写配置的数据\n", "    url_='http://127.0.0.1:5000/api/report_etl/trigger_batch_insert'\n", "    payload_ = {\n", "        \"spreadsheet_token\": spreadsheet_token,\n", "        \"sheet_id\": sheet_id\n", "    }\n", "    response = requests.post(url_, headers=headers,json=payload_)\n", "    print(response.json())\n", "\n", "    # 填写rag数据\n", "    url_ = \"http://127.0.0.1:5000/api/rag2sheet\"\n", "\n", "    payload_ = {\n", "        \"user_id\":USER_ID,\n", "        \"sheet_token\":sheet_token,\n", "        \"user_token\":USER_TOEKN\n", "    }\n", "\n", "    response = requests.post(url_, json=payload_, headers=headers)\n", "    print(response.status_code)\n", "    print(response.json())\n", "\n", "    break \n", "\n", "\n", "\n"]}, {"cell_type": "code", "execution_count": 7, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["doxcnOqfuj8SXyNNOLoAJy0Qxgh FLp2sEDUshX928t41flcWdJZnve_AfGBcX FLp2sEDUshX928t41flcWdJZnve AfGBcX\n", "{'code': '0', 'data': {'_flowExecutionID': 1831426645454868}, 'message': [{'language_code': 1033, 'text': 'Success'}, {'language_code': 2052, 'text': '成功'}], 'status': 'success', 'success': True}\n", "200\n", "{'message': '表格写入成功', 'success': True}\n"]}], "source": ["# 返回各个表格的token\n", "import requests\n", "\n", "block_id = None\n", "# url = \"http://cqctool-cbao.bytedance.net/api/feishu/get_sheet_block\"\n", "url = \"http://127.0.0.1:5000/api/feishu/get_sheet_block\"\n", "payload = {\n", "    \"user_id\":USER_ID,\n", "    \"url\": doc_url,  # 替换为实际飞书文档链接\n", "    \"user_token\": USER_TOEKN  # 如无需用户token可去掉此行或设为None\n", "}\n", "headers = {\"Content-Type\": \"application/json\"}\n", "while True:\n", "    response = requests.post(url, json=payload, headers=headers)\n", "    js = response.json()\n", "    if not js['block_id']:\n", "        break\n", "\n", "    block_id = js['block_id']\n", "    sheet_token = js['sheet_token']\n", "    spreadsheet_token = sheet_token.strip().split('_')[0]\n", "    sheet_id = sheet_token.strip().split('_')[1]\n", "    print(block_id, sheet_token, spreadsheet_token, sheet_id)\n", "\n", "    # 填写配置的数据\n", "    url_='http://127.0.0.1:5000/api/report_etl/trigger_batch_insert'\n", "    payload_ = {\n", "        \"spreadsheet_token\": spreadsheet_token,\n", "        \"sheet_id\": sheet_id\n", "    }\n", "    response = requests.post(url_, headers=headers,json=payload_)\n", "    print(response.json())\n", "\n", "    # 填写rag数据\n", "    url_ = \"http://127.0.0.1:5000/api/rag2sheet\"\n", "\n", "    payload_ = {\n", "        \"user_id\":USER_ID,\n", "        \"sheet_token\":sheet_token,\n", "        \"user_token\":USER_TOEKN\n", "    }\n", "\n", "    response = requests.post(url_, json=payload_, headers=headers)\n", "    print(response.status_code)\n", "    print(response.json())\n", "\n", "    break \n", "\n", "\n", "\n"]}, {"cell_type": "code", "execution_count": 8, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["doxcnndgpHlMCf5lAZGygTsVwwc FLp2sEDUshX928t41flcWdJZnve_ikFg4k FLp2sEDUshX928t41flcWdJZnve ikFg4k\n", "{'code': '0', 'data': {'_flowExecutionID': 1831426698406970}, 'message': [{'language_code': 1033, 'text': 'Success'}, {'language_code': 2052, 'text': '成功'}], 'status': 'success', 'success': True}\n", "200\n", "{'message': '无需写入', 'success': True}\n"]}], "source": ["# 返回各个表格的token\n", "import requests\n", "\n", "block_id = None\n", "# url = \"http://cqctool-cbao.bytedance.net/api/feishu/get_sheet_block\"\n", "url = \"http://127.0.0.1:5000/api/feishu/get_sheet_block\"\n", "payload = {\n", "    \"user_id\":USER_ID,\n", "    \"url\": doc_url,  # 替换为实际飞书文档链接\n", "    \"user_token\": USER_TOEKN  # 如无需用户token可去掉此行或设为None\n", "}\n", "headers = {\"Content-Type\": \"application/json\"}\n", "while True:\n", "    response = requests.post(url, json=payload, headers=headers)\n", "    js = response.json()\n", "    if not js['block_id']:\n", "        break\n", "\n", "    block_id = js['block_id']\n", "    sheet_token = js['sheet_token']\n", "    spreadsheet_token = sheet_token.strip().split('_')[0]\n", "    sheet_id = sheet_token.strip().split('_')[1]\n", "    print(block_id, sheet_token, spreadsheet_token, sheet_id)\n", "\n", "    # 填写配置的数据\n", "    url_='http://127.0.0.1:5000/api/report_etl/trigger_batch_insert'\n", "    payload_ = {\n", "        \"spreadsheet_token\": spreadsheet_token,\n", "        \"sheet_id\": sheet_id\n", "    }\n", "    response = requests.post(url_, headers=headers,json=payload_)\n", "    print(response.json())\n", "\n", "    # 填写rag数据\n", "    url_ = \"http://127.0.0.1:5000/api/rag2sheet\"\n", "\n", "    payload_ = {\n", "        \"user_id\":USER_ID,\n", "        \"sheet_token\":sheet_token,\n", "        \"user_token\":USER_TOEKN\n", "    }\n", "\n", "    response = requests.post(url_, json=payload_, headers=headers)\n", "    print(response.status_code)\n", "    print(response.json())\n", "\n", "    break \n", "\n", "\n", "\n"]}, {"cell_type": "code", "execution_count": 9, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["doxcnAaKVdtUppeOchKvR1J1FZb FLp2sEDUshX928t41flcWdJZnve_F8y9u1 FLp2sEDUshX928t41flcWdJZnve F8y9u1\n", "{'code': '0', 'data': {'_flowExecutionID': 1831426700201079}, 'message': [{'language_code': 1033, 'text': 'Success'}, {'language_code': 2052, 'text': '成功'}], 'status': 'success', 'success': True}\n", "200\n", "{'message': '表格写入成功', 'success': True}\n"]}], "source": ["# 返回各个表格的token\n", "import requests\n", "\n", "block_id = None\n", "# url = \"http://cqctool-cbao.bytedance.net/api/feishu/get_sheet_block\"\n", "url = \"http://127.0.0.1:5000/api/feishu/get_sheet_block\"\n", "payload = {\n", "    \"user_id\":USER_ID,\n", "    \"url\": doc_url,  # 替换为实际飞书文档链接\n", "    \"user_token\": USER_TOEKN  # 如无需用户token可去掉此行或设为None\n", "}\n", "headers = {\"Content-Type\": \"application/json\"}\n", "while True:\n", "    response = requests.post(url, json=payload, headers=headers)\n", "    js = response.json()\n", "    if not js['block_id']:\n", "        break\n", "\n", "    block_id = js['block_id']\n", "    sheet_token = js['sheet_token']\n", "    spreadsheet_token = sheet_token.strip().split('_')[0]\n", "    sheet_id = sheet_token.strip().split('_')[1]\n", "    print(block_id, sheet_token, spreadsheet_token, sheet_id)\n", "\n", "    # 填写配置的数据\n", "    url_='http://127.0.0.1:5000/api/report_etl/trigger_batch_insert'\n", "    payload_ = {\n", "        \"spreadsheet_token\": spreadsheet_token,\n", "        \"sheet_id\": sheet_id\n", "    }\n", "    response = requests.post(url_, headers=headers,json=payload_)\n", "    print(response.json())\n", "\n", "    # 填写rag数据\n", "    url_ = \"http://127.0.0.1:5000/api/rag2sheet\"\n", "\n", "    payload_ = {\n", "        \"user_id\":USER_ID,\n", "        \"sheet_token\":sheet_token,\n", "        \"user_token\":USER_TOEKN\n", "    }\n", "\n", "    response = requests.post(url_, json=payload_, headers=headers)\n", "    print(response.status_code)\n", "    print(response.json())\n", "\n", "    break \n", "\n", "\n", "\n"]}, {"cell_type": "code", "execution_count": 10, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["doxcnbO6J2JYPXnYe5nlb98T6Lc FLp2sEDUshX928t41flcWdJZnve_QLeQrG FLp2sEDUshX928t41flcWdJZnve QLeQrG\n", "{'code': '0', 'data': {'_flowExecutionID': 1831426802076955}, 'message': [{'language_code': 1033, 'text': 'Success'}, {'language_code': 2052, 'text': '成功'}], 'status': 'success', 'success': True}\n", "200\n", "{'message': '表格写入成功', 'success': True}\n"]}], "source": ["# 返回各个表格的token\n", "import requests\n", "\n", "block_id = None\n", "# url = \"http://cqctool-cbao.bytedance.net/api/feishu/get_sheet_block\"\n", "url = \"http://127.0.0.1:5000/api/feishu/get_sheet_block\"\n", "payload = {\n", "    \"user_id\":USER_ID,\n", "    \"url\": doc_url,  # 替换为实际飞书文档链接\n", "    \"user_token\": USER_TOEKN  # 如无需用户token可去掉此行或设为None\n", "}\n", "headers = {\"Content-Type\": \"application/json\"}\n", "while True:\n", "    response = requests.post(url, json=payload, headers=headers)\n", "    js = response.json()\n", "    if not js['block_id']:\n", "        break\n", "\n", "    block_id = js['block_id']\n", "    sheet_token = js['sheet_token']\n", "    spreadsheet_token = sheet_token.strip().split('_')[0]\n", "    sheet_id = sheet_token.strip().split('_')[1]\n", "    print(block_id, sheet_token, spreadsheet_token, sheet_id)\n", "\n", "    # 填写配置的数据\n", "    url_='http://127.0.0.1:5000/api/report_etl/trigger_batch_insert'\n", "    payload_ = {\n", "        \"spreadsheet_token\": spreadsheet_token,\n", "        \"sheet_id\": sheet_id\n", "    }\n", "    response = requests.post(url_, headers=headers,json=payload_)\n", "    print(response.json())\n", "\n", "    # 填写rag数据\n", "    url_ = \"http://127.0.0.1:5000/api/rag2sheet\"\n", "\n", "    payload_ = {\n", "        \"user_id\":USER_ID,\n", "        \"sheet_token\":sheet_token,\n", "        \"user_token\":USER_TOEKN\n", "    }\n", "\n", "    response = requests.post(url_, json=payload_, headers=headers)\n", "    print(response.status_code)\n", "    print(response.json())\n", "\n", "    break \n", "\n", "\n", "\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "base", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.2"}}, "nbformat": 4, "nbformat_minor": 2}