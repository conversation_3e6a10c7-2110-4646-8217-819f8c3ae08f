# 经分月报自动化生成系统

这是一个基于 Flask 的经分月报自动化生成系统，可以帮助用户快速生成和分析经分月报。

## 功能特点

- 支持飞书文档和表格嵌入与解析
- 智能对话式交互界面
- 自动化报告生成与数据分析
- 实时日志记录
- 响应式设计，适配多种设备
- 用户注册与认证系统

## 系统要求

- Python 3.7+
- Flask 2.0.1
- 现代浏览器（Chrome、Firefox、Safari 等）

## 安装步骤

1. 克隆仓库到本地

```bash
git clone https://github.com/yourusername/business_analysis_report.git
cd business_analysis_report
```

2. 创建并激活虚拟环境
```bash
python -m venv venv
source venv/bin/activate  # macOS/Linux
 ```

3. 安装依赖
```bash
pip install -r requirements.txt
 ```

## 启动方式



### 启动lightRAG服务
```bash
cd lightrag
pip install -e ".[api]"
lightrag-server
```

### 启动月报应用
直接使用 app.py 启动
```bash
python app.py
 ```
或者使用 run.py 启动
```bash
python run.py
 ```

### 访问应用
1. 访问lightrag 
http://0.0.0.0:9621/webui/#/

2. 访问应用
启动成功后，在浏览器中访问：
- 主页： http://127.0.0.1:5001/
- 仪表盘： http://127.0.0.1:5001/dashboard
## 配置说明
项目使用 YAML 格式的配置文件：

pip install -e ".[api]"
lightrag-server
lightrag-gunicorn --workers 4


2. 直接使用 app.py 启动
```bash
python app.py
 ```

或者使用 run.py 启动

```bash
python run.py
 ```

3. 访问lightrag 
http://0.0.0.0:9621/webui/#/


4. 访问应用
启动成功后，在浏览器中访问：

- 主页： http://127.0.0.1:5001/
- 仪表盘： http://127.0.0.1:5001/dashboard
## 配置说明
项目使用 YAML 格式的配置文件：

- 基础配置： config/config.yaml
- 环境特定配置： config/config.{env}.yaml （其中 env 为环境变量 ENV 的值，默认为 dev）
## 项目结构
```plaintext
cbao_business_analysis_report/
├── app/                    # 应用主目录
│   ├── api/                # API接口
│   │   ├── feishu_doc.py   # 飞书文档相关API
│   │   ├── registration.py # 用户注册API
│   │   └── report_etl.py   # 报告ETL处理API
│   ├── auth/               # 认证相关模块
│   ├── feishu_doc/         # 飞书文档处理模块
│   ├── feishu_sheet/       # 飞书表格处理模块
│   ├── llm/                # 大语言模型集成
│   ├── services/           # 业务服务层
│   ├── static/             # 静态资源
│   │   ├── css/            # 样式文件
│   │   ├── js/             # JavaScript文件
│   │   └── img/            # 图片资源
│   ├── templates/          # HTML模板
│   ├── tests/              # 测试代码
│   ├── utils/              # 工具函数
│   │   └── config_loader.py # 配置加载器
│   ├── views/              # 视图函数
│   └── __init__.py         # 应用初始化
├── config/                 # 配置文件目录
│   └── config.yaml         # 基础配置
├── data/                   # 数据存储目录
│   └── users/              # 用户数据
├── app.py                  # 应用入口
├── config.py               # Flask配置
├── requirements.txt        # 依赖列表
├── run.py                  # 启动脚本
└── README.md               # 项目说明
 ```

## 常见问题
Q: 飞书文档无法加载怎么办？ A: 请确保文档已设置为"任何人可查看"，或者提供有效的用户令牌。

Q: 如何配置不同环境？ A: 创建对应的 config.{env}.yaml 文件，并设置环境变量 ENV={env} 。

Q: 如何退出虚拟环境？ A: 在命令行中输入 deactivate 。

