# coding: utf-8

from __future__ import absolute_import
from __future__ import division
from __future__ import print_function
from __future__ import unicode_literals

import logging.config

import flask
from flask_cors import CORS


from views import views


config = {
    'version': 1,
    'disable_existing_loggers': False,
    'formatters': {
        'default': {
            'format': '%(asctime)s %(levelname)s %(message)s'
        }
    },
    'handlers': {
        'log_agent': {
            'level': 'INFO',
            'class': 'bytedlogger.StreamLogHandler',
        },
        'console': {
            'level': 'INFO',
            'class': 'logging.StreamHandler',
            'formatter': 'default',
        }
    },
    'root': {
        'handlers': ['log_agent', 'console'],
        'level': "INFO"
    }
}
logging.config.dictConfig(config)

app = flask.Flask(__name__)
# 添加跨域支持
CORS(app)


app.register_blueprint(views)


from app.api.registration import registration_bp
from app.api.rag import rag_bp
from app.api.rag2sheet import rag2sheet_bp
from app.api.feishu_doc import feishu_bp
from app.api.report_etl import report_etl_bp
from app.api.auth import auth_bp


app.register_blueprint(registration_bp, url_prefix='/api')
app.register_blueprint(feishu_bp, url_prefix='/api/feishu')
app.register_blueprint(report_etl_bp, url_prefix='/api/report_etl')
app.register_blueprint(rag_bp,url_prefix='/api')
app.register_blueprint(rag2sheet_bp,url_prefix='/api')
app.register_blueprint(auth_bp,url_prefix='/api')







# if __name__ == '__main__':
#     #Never run this in production environment!
#     app.run(debug=True)
