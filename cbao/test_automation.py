import sys
import os
import importlib
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(os.path.abspath(__file__)), '..')))
import yaml
import requests
from requests_toolbelt import MultipartEncoder
import pyautogui
import time
from PIL import Image
import io
import base64
import json
from app.feishu_sheet.feishu_sheet import read_feishu_sheet_user_token
from app.auth.auth import get_token
import base64
import random

class TestAutomationTool:
    def __init__(self, config_path):
        # 加载配置文件
        self.config = self.load_config(config_path)
        # 初始化飞书表格参数
        self.feishu_token = self.config['feishu']['feishu_token']
        self.spreadsheet_token = self.config['feishu']['spreadsheet_token']
        self.sheet_id = self.config['feishu']['sheet_id']
        self.app_id = self.config['feishu']['app_id']
        self.app_secret = self.config['feishu']['app_secret']
        # 新增：加载输入输出配置
        self.input_row = self.config['feishu']['input_row']
        self.input_col = self.config['feishu']['input_col']
        self.output_row = self.config['feishu']['output_row']
        self.output_col = self.config['feishu']['output_col']
        # 初始化LLM参数
        self.llm_api_key = self.config['llm']['api_key']
        self.llm_api_url = self.config['llm']['base_url']

        #生成token
        app_token = get_token(self.app_id, self.app_secret)
        # 将app_token写入config.yaml中的user_access_token
        config_path = os.path.join(os.path.dirname(__file__), 'config.yaml')
        with open(config_path, 'r') as f:
            config = yaml.safe_load(f)
        config['feishu']['user_access_token'] = app_token
        with open(config_path, 'w') as f:
            yaml.safe_dump(config, f, sort_keys=False, allow_unicode=True)

        self.user_access_token = self.config['feishu']['user_access_token']
        

    def load_config(self, config_path):
        with open(config_path, 'r', encoding='utf-8') as f:
            return yaml.safe_load(f)

    def get_feishu_sheet_data(self):#token用来拼url
        # 调用现有飞书表格读取函数
        sheet_data = read_feishu_sheet_user_token(
            token=self.feishu_token,
            user_access_token=self.user_access_token,
        )
        
        # 新增：根据配置过滤行列数据并保留原始行号
        if 'values' in sheet_data.get('data', {}).get('valueRange', {}):
            all_rows = sheet_data['data']['valueRange']['values']
            start_row, end_row = self.parse_row_range(self.input_row)
            start_col, end_col = self.parse_col_range(self.input_col)
            
            # 切片获取指定范围数据（注意：表格行号从1开始，Python列表从0开始）
            filtered_rows = all_rows[start_row-1:end_row]  # 行切片
            filtered_data = [row[start_col:end_col+1] for row in filtered_rows]  # 列切片
            
            # 获取原始行号（从start_row开始）
            original_row_numbers = range(start_row, start_row + len(filtered_data))
            # 返回包含原始行号和问题数据的元组列表
            return list(zip(original_row_numbers, filtered_data))
        return []


    def initialize_login(self):
        """初始化登录，只需要登录一次"""
        if os.path.exists("./auth_state.json"):
            print("发现已保存的登录状态，将复用登录信息")
            return

        print("首次运行，需要登录...")
        from playwright.sync_api import sync_playwright

        with sync_playwright() as playwright:
            browser = playwright.chromium.launch(headless=False)
            context = browser.new_context()
            page = context.new_page()

            page.goto('https://cqc-tool-c-bot.gf-boe.bytedance.net/login')
            print("请在浏览器中完成登录...")

            # 等待用户登录完成
            page.wait_for_url('https://cqc-tool-c-bot.gf-boe.bytedance.net/home?agentId=cqcDataAssistant', timeout=120000)

            # 保存登录状态
            context.storage_state(path="./auth_state.json")
            print("✅ 登录状态已保存，后续运行将自动登录")

            context.close()
            browser.close()

    def run_test(self):
        # 0. 初始化登录
        self.initialize_login()

        # 1. 读取飞书表格数据
        print("正在读取飞书表格数据...")
        sheet_data = self.get_feishu_sheet_data()
        print(f"成功读取{len(sheet_data)}条测试数据")

        # 2. 创建截图保存目录
        SCREENSHOT_DIR = os.path.join(os.path.dirname(__file__), 'screenshots')
        os.makedirs(SCREENSHOT_DIR, exist_ok=True)
        print(f"截图将保存至: {SCREENSHOT_DIR}")

        # 3. 并行处理所有问题
        from playwright.sync_api import sync_playwright
        from concurrent.futures import ThreadPoolExecutor

        def process_question(idx, original_row, question):
            """处理单个问题的函数，使用共享浏览器实例"""
            try:
                with sync_playwright() as playwright:
                    # 从配置获取是否使用无头模式
                    headless_mode = self.config.get('feishu', {}).get('headless_browser', False)

                    # 使用共享的用户数据目录，但启动独立的浏览器实例
                    browser = playwright.chromium.launch(
                        headless=headless_mode,
                        args=['--no-sandbox', '--disable-dev-shm-usage']
                    )

                    # 创建新的上下文，可以复用登录状态
                    context = browser.new_context(
                        storage_state="./auth_state.json" if os.path.exists("./auth_state.json") else None
                    )
                    page = context.new_page()

                    # 直接导航到主页（登录状态已保存）
                    page.goto('https://cqc-tool-c-bot.gf-boe.bytedance.net/home?agentId=cqcDataAssistant')

                    # 导航到提问页面
                    page.goto('https://cqc-tool-c-bot.gf-boe.bytedance.net/home?agentId=cqcDataAssistant')

                    # 输入问题
                    input_box = page.get_by_role("textbox")
                    input_box.fill("")  # 清空输入框
                    input_box.fill(question)
                    input_box.press("Enter")

                    # 等待回答完成 - 使用视觉大模型检测
                    print(f"问题已提交: {question[:30]}... 等待回答完成")
                    screenshot_path = self.wait_for_answer_and_screenshot(page, question, SCREENSHOT_DIR)

                    if random.random() < 0.1:
                        self.user_access_token = get_token(self.app_id, self.app_secret)
                        print("刷新token")

                    if screenshot_path:
                        print(f"已完成问题 {idx}: {question[:30]}... 截图保存至: {screenshot_path}")
                    else:
                        print(f"截图失败: {question[:30]}...")

                    # 上传图片到飞书并更新表格
                    if screenshot_path:
                        try:
                            self.upload_image_and_update_sheet(screenshot_path, original_row)
                        except Exception as e:
                            print(f"上传图片或更新表格失败: {str(e)}")
                    else:
                        print("截图失败，跳过上传")

                    # 关闭浏览器
                    context.close()
                    browser.close()

            except Exception as e:
                print(f"处理问题时出错: {str(e)}")

        # 4. 使用线程池控制并发数量,从配置文件获取最大并发线程数
        max_workers = self.config.get('feishu', {}).get('max_concurrent_threads')
        print(f"使用线程池，最大并发数: {max_workers}")

        with ThreadPoolExecutor(max_workers=max_workers) as executor:
            # 提交所有任务到线程池
            futures = []
            for idx, (original_row, row_data) in enumerate(sheet_data, start=1):
                question = row_data[0]  # 获取问题文本
                future = executor.submit(process_question, idx, original_row, question)
                futures.append(future)
                time.sleep(5)  # 稍微延迟避免同时启动过多任务

            # 等待所有任务完成
            for future in futures:
                try:
                    future.result()  # 获取结果，如果有异常会抛出
                except Exception as e:
                    print(f"处理问题时发生错误: {str(e)}")

        print("所有问题处理完成")

    def wait_for_answer_and_screenshot(self, page, question, screenshot_dir):
        try:
            # 生成安全的截图文件名
            safe_question = "".join([c for c in question if c.isalnum() or c in ' _-']).strip()
            screenshot_path = os.path.join(screenshot_dir, f'{safe_question}.png')

            # 从配置获取视觉检测参数
            vision_config = self.config.get('feishu', {}).get('vision_detection', {})
            max_wait_time = vision_config.get('max_wait_time')
            check_interval = vision_config.get('check_interval')
            target_text = vision_config.get('target_text')
            max_retries = vision_config.get('max_retries', 3)  # 最大重试次数

            retry_count = 0

            while retry_count <= max_retries:
                if retry_count > 0:
                    print(f"🔄 第 {retry_count} 次重试")

                elapsed_time = 0
                print(f"开始等待回答完成，检测目标文本：'{target_text}'")

                while elapsed_time < max_wait_time:
                    # 等待一段时间
                    time.sleep(check_interval)
                    elapsed_time += check_interval

                    # 截取当前页面进行检测
                    temp_screenshot = os.path.join(screenshot_dir, f'temp_check_{safe_question}.png')
                    page.screenshot(path=temp_screenshot)

                    # 检测错误信息
                    if self.detect_error_text_with_vision(temp_screenshot):
                        print("❌ 检测到执行错误，准备重新输入问题")
                        if os.path.exists(temp_screenshot):
                            os.remove(temp_screenshot)

                        # 重新输入问题
                        self.retry_input_question(page, question)
                        retry_count += 1
                        break  # 跳出内层循环，开始新的检测周期

                    # 使用视觉大模型检测是否包含目标文本
                    if self.detect_target_text_with_vision(temp_screenshot):
                        print(f"✅ 检测到目标文本！回答已完成，用时 {elapsed_time} 秒")

                        if os.path.exists(temp_screenshot):
                            os.remove(temp_screenshot)

                        # 截取问答卡片
                        return self.capture_qa_card(page, screenshot_path)

                    if os.path.exists(temp_screenshot):
                        os.remove(temp_screenshot)

                    print(f"等待中... ({elapsed_time}/{max_wait_time}秒)")
                else:
                    # 如果是因为超时退出的while循环
                    if retry_count < max_retries:
                        print(f"⚠️ 等待超时，但未达到最大重试次数，继续重试")
                        retry_count += 1
                    else:
                        print(f"⚠️ 达到最大重试次数，停止重试")
                        break

            # 最终截图
            page.screenshot(path=screenshot_path)
            return screenshot_path

        except Exception as e:
            print(f"等待和截图过程出错: {str(e)}")
            return None

    def detect_target_text_with_vision(self, image_path):
        """检测目标完成文本"""
        return self.detect_text_with_vision(image_path, "target")

    def detect_error_text_with_vision(self, image_path):
        """检测错误文本"""
        return self.detect_text_with_vision(image_path, "error")

    def detect_text_with_vision(self, image_path, detection_type):
        """通用的视觉文本检测方法"""
        try:
            # 读取图片并转换为base64
            with open(image_path, "rb") as f:
                image_data = base64.b64encode(f.read()).decode('utf-8')

            # 构建请求数据
            llm_config = self.config.get('llm', {})
            api_key = llm_config.get('api_key')
            base_url = llm_config.get('base_url')
            model_name = llm_config.get('model_name')

            # 根据检测类型设置不同的文本和提示
            if detection_type == "target":
                vision_config = self.config.get('feishu', {}).get('vision_detection', {})
                target_text = vision_config.get('target_text')
                prompt_text = f"请检查这张图片中是否包含蓝色的文字'{target_text}'。如果包含，请回答'是'，如果不包含，请回答'否'。只需要回答'是'或'否'，不需要其他解释。"
            elif detection_type == "error":
                prompt_text = "请检查这张图片中是否包含红色的文字'执行错误，请重试'。如果包含，请回答'是'，如果不包含，请回答'否'。只需要回答'是'或'否'，不需要其他解释。"
            else:
                return False

            headers = {
                "Authorization": f"Bearer {api_key}",
                "Content-Type": "application/json"
            }

            data = {
                "model": model_name,
                "messages": [
                    {
                        "role": "user",
                        "content": [
                            {
                                "type": "text",
                                "text": prompt_text
                            },
                            {
                                "type": "image_url",
                                "image_url": {
                                    "url": f"data:image/png;base64,{image_data}"
                                }
                            }
                        ]
                    }
                ],
                "max_tokens": 10,
                "temperature": 0
            }

            response = requests.post(f"{base_url}/chat/completions",
                                   headers=headers,
                                   json=data,
                                   timeout=30)

            if response.status_code == 200:
                result = response.json()
                if 'choices' in result and len(result['choices']) > 0:
                    answer = result['choices'][0]['message']['content'].strip()
                    detection_name = "目标文本" if detection_type == "target" else "错误文本"
                    print(f"{detection_name}检测结果: {answer}")
                    return answer == '是'
                else:
                    print("❌ 视觉检测响应格式异常")
                    return False
            else:
                print(f"❌ 视觉检测API调用失败: {response.status_code}")
                return False

        except Exception as e:
            print(f"❌ 视觉检测异常: {str(e)}")
            return False

    def retry_input_question(self, page, question):
        """重新输入问题"""
        try:
            print(f"🔄 重新输入问题: {question}")

            # 等待一下让页面稳定
            time.sleep(2)

            # 查找输入框并重新输入
            input_box = page.get_by_role("textbox")
            input_box.fill("")  # 清空输入框
            time.sleep(1)
            input_box.fill(question)  # 重新输入问题
            time.sleep(1)
            input_box.press("Enter")  # 提交

            print("✅ 问题重新输入完成")
            time.sleep(3)  # 等待提交处理

        except Exception as e:
            print(f"❌ 重新输入问题失败: {str(e)}")

    def capture_qa_card(self, page, screenshot_path):
        """
        使用固定边界参数截取问答卡片
        """
        try:
            print("使用固定边界截取问答卡片...")

            viewport = page.viewport_size
            width = viewport['width']
            height = viewport['height']

            # 从配置获取固定边界参数
            screenshot_config = self.config.get('feishu', {}).get('screenshot_boundaries', {})

            # 固定边界参数（像素值）
            left = screenshot_config.get('left')      # 左边界
            top = screenshot_config.get('top')        # 上边界
            right = screenshot_config.get('right')   # 右边界
            bottom = screenshot_config.get('bottom')  # 下边界

            # 计算截图区域
            clip_area = {
                'x': left,
                'y': top,
                'width': min(right - left, width - left),
                'height': min(bottom - top, height - top)
            }

            print(f"截图区域: {clip_area}")
            page.screenshot(path=screenshot_path, clip=clip_area)
            return screenshot_path

        except Exception as e:
            print(f"固定边界截图失败: {str(e)}")
            # 回退到整页截图
            page.screenshot(path=screenshot_path)
            return screenshot_path



    def parse_row_range(self, row_range_str): #解析行范围字符串（如"5"或"2-31"）为起始行和结束行
        parts = row_range_str.split('-')
        start_row = int(parts[0])
        end_row = int(parts[1]) if len(parts) > 1 else start_row
        return start_row, end_row

    def parse_col_range(self, col_range_str): #解析列范围字符串（如"A"或"A-C"）为起始列索引和结束列索引
        parts = col_range_str.split('-')
        start_col = ord(parts[0].upper()) - ord('A')
        end_col = ord(parts[1].upper()) - ord('A') if len(parts) > 1 else start_col
        return start_col, end_col

    def upload_image_and_update_sheet(self, image_path, row ,max_try =3):
        """
        使用飞书表格的 values_image API 直接插入图片
        """
        while max_try>0:
            try:
                print(f"正在直接插入图片到表格: {os.path.basename(image_path)}")

                # 读取图片并转换为base64
                with open(image_path, "rb") as f:
                    fb = f.read()
                    # 处理base64编码
                    fb = base64.b64encode(fb).decode('utf-8')

                # 构建API URL和数据
                spreadsheet_token = self.spreadsheet_token.strip().split('_')[0]
                sheet_id = self.spreadsheet_token.strip().split('_')[1]
                position = f"{self.output_col}{row}"

                url = f"https://open.feishu.cn/open-apis/sheets/v2/spreadsheets/{spreadsheet_token}/values_image"

                data = {
                    "range": f"{sheet_id}!{position}:{position}",
                    "image": fb,
                    "name": os.path.basename(image_path),
                }

                headers = {
                    "Authorization": f"Bearer {self.user_access_token}",
                    "Content-Type": "application/json"
                }

                # 发送请求
                response = requests.post(url, data=json.dumps(data), headers=headers)

                print(f"图片插入 Status Code: {response.status_code}")
                result = response.json()
                print(f"图片插入 Response: {result}")

                if response.status_code == 200 and result.get('code') == 0:
                    print(f"✅ 图片成功插入到表格第{row}行: {position}")
                    return True
                else:
                    print(f"❌ 图片插入失败: {result}")
                    max_try-=1
                    time.sleep(1)
            except Exception as e:
                print(f"❌ 图片插入异常: {str(e)}")
                time.sleep(1)
                max_try-=1
        return False

if __name__ == "__main__":
    # 配置文件路径
    config_path = os.path.join(os.path.dirname(__file__), 'config.yaml')
    # 创建测试工具实例
    test_tool = TestAutomationTool(config_path)
    # 运行测试流程
    test_tool.run_test()

