{"cookies": [{"name": "CAS_SESSION", "value": "184ba2c0f732ac8e10838a0e83cf9ed4", "domain": "cloud.bytedance.net", "path": "/auth/api/v1", "expires": 1751276629.799069, "httpOnly": true, "secure": false, "sameSite": "Lax"}, {"name": "__Host_CAS_SESSION_ss", "value": "184ba2c0f732ac8e10838a0e83cf9ed4", "domain": "cloud.bytedance.net", "path": "/auth/api/v1", "expires": 1751276629.799321, "httpOnly": true, "secure": true, "sameSite": "None"}, {"name": "CAS_SESSION_ss", "value": "184ba2c0f732ac8e10838a0e83cf9ed4", "domain": "cloud.bytedance.net", "path": "/auth/api/v1", "expires": 1751276629.800017, "httpOnly": true, "secure": true, "sameSite": "None"}, {"name": "CAS_SESSION_WEB", "value": "184ba2c0f732ac8e10838a0e83cf9ed4", "domain": "cloud.bytedance.net", "path": "/web_api", "expires": 1751276629.799998, "httpOnly": true, "secure": false, "sameSite": "Lax"}, {"name": "CAS_SESSION_WEB_ss", "value": "184ba2c0f732ac8e10838a0e83cf9ed4", "domain": "cloud.bytedance.net", "path": "/web_api", "expires": 1751276629.800035, "httpOnly": true, "secure": true, "sameSite": "None"}, {"name": "__Host_CAS_SESSION_WEB_ss", "value": "184ba2c0f732ac8e10838a0e83cf9ed4", "domain": "cloud.bytedance.net", "path": "/web_api", "expires": 1751276629.800051, "httpOnly": true, "secure": true, "sameSite": "None"}, {"name": "CAS_SESSION_API_ss", "value": "184ba2c0f732ac8e10838a0e83cf9ed4", "domain": "cloud.bytedance.net", "path": "/api", "expires": 1751276629.799275, "httpOnly": true, "secure": true, "sameSite": "None"}, {"name": "__Host_CAS_SESSION_API_ss", "value": "184ba2c0f732ac8e10838a0e83cf9ed4", "domain": "cloud.bytedance.net", "path": "/api", "expires": 1751276629.799878, "httpOnly": true, "secure": true, "sameSite": "None"}, {"name": "CAS_SESSION_API", "value": "184ba2c0f732ac8e10838a0e83cf9ed4", "domain": "cloud.bytedance.net", "path": "/api", "expires": 1751276629.799954, "httpOnly": true, "secure": false, "sameSite": "Lax"}, {"name": "bdsso_web_did", "value": "bdsso_web_did:eb1f2b06-9373-4cfb-96d8-12e7919a5fd9", "domain": "sso.bytedance.com", "path": "/", "expires": 1751967829.731827, "httpOnly": true, "secure": true, "sameSite": "None"}, {"name": "user_language", "value": "zh-CN", "domain": "sso.bytedance.com", "path": "/", "expires": 1751967806, "httpOnly": false, "secure": true, "sameSite": "Lax"}, {"name": "mfa_cache", "value": "undefined", "domain": "sso.bytedance.com", "path": "/", "expires": 1751967806, "httpOnly": false, "secure": true, "sameSite": "Lax"}, {"name": "signature", "value": "a8e9e1906fad9c0062d69c98d91b78a4:1750671807", "domain": "sso.bytedance.com", "path": "/", "expires": 1751967807.836338, "httpOnly": true, "secure": true, "sameSite": "None"}, {"name": "csrftoken", "value": "2ee14638-1aea-441f-ad22-626976006a0f", "domain": "sso.bytedance.com", "path": "/", "expires": 1751967807.836384, "httpOnly": false, "secure": true, "sameSite": "None"}, {"name": "bd_sso_sid_c4f15a", "value": "1740157d567842b99be2d6221b08ec78", "domain": "sso.bytedance.com", "path": "/", "expires": 1751967829.731996, "httpOnly": true, "secure": true, "sameSite": "None"}, {"name": "bd_sso_6nskq38", "value": "*******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************", "domain": "sso.bytedance.com", "path": "/", "expires": 1751967827.98703, "httpOnly": true, "secure": true, "sameSite": "None"}, {"name": "idp_cache", "value": "lark", "domain": "sso.bytedance.com", "path": "/", "expires": 1751967827, "httpOnly": false, "secure": true, "sameSite": "Lax"}, {"name": "sessionid", "value": "", "domain": "sso.bytedance.com", "path": "/", "expires": 1751967829.731954, "httpOnly": true, "secure": true, "sameSite": "None"}, {"name": "bd_sso_3b6da9", "value": "*******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************", "domain": ".bytedance.net", "path": "/", "expires": 1782207829.800099, "httpOnly": false, "secure": false, "sameSite": "Lax"}], "origins": [{"origin": "https://cqc-tool-c-bot.gf-boe.bytedance.net", "localStorage": [{"name": "__tea_cache_tokens_780327", "value": "{\"user_unique_id\":\"yanwenjie.666\",\"timestamp\":1750671830142,\"_type_\":\"default\"}"}, {"name": "CLOUD_JWT_MAP", "value": "{\"online\":\"*************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************\"}"}, {"name": "__cloud_jwt", "value": "*************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************"}, {"name": "__tea_cache_first_780327", "value": "1"}]}, {"origin": "https://sso.bytedance.com", "localStorage": [{"name": "NGID_STORAGE_KEY", "value": "{\"zh-CN\":{\"200\":\"服务器成功返回请求的数据。\",\"201\":\"新建或修改数据成功。\",\"202\":\"一个请求已经进入后台排队（异步任务）。\",\"204\":\"删除数据成功。\",\"400\":\"发出的请求有错误，服务器没有进行新建或修改数据的操作。\",\"401\":\"用户没有权限（令牌、用户名、密码错误）。\",\"403\":\"糟糕！页面无权访问\",\"404\":\"抱歉，您访问的页面不存在～\",\"406\":\"请求的格式不可得。\",\"410\":\"请求的资源被永久删除，且不会再得到的。\",\"422\":\"当创建一个对象时，发生一个验证错误。\",\"500\":\"服务器发生错误，请检查服务器。\",\"502\":\"网关错误。\",\"503\":\"服务不可用，服务器暂时过载或维护。\",\"504\":\"网关超时。\",\"00fffa959cd74722828add051852a130\":\"短信验证身份\",\"0974fb72f657f7be1cc33bd253aa0c0f\":\"忘记密码\",\"43cecee174b5b6c363b9bc8bd3389312\":\"OTP 验证身份\",\"log_in\":\"登录\",\"5f5351fab0db28aaf61b343aaca8e4fc\":\"{seconds}s后重新发送\",\"670d631e181f18da38c698eadc6d13e9\":\"设置新密码\",\"6b723436d0a1a354e84c4fcb627d0877\":\"提交验证\",\"899687f035a9eeeeed94b9c55596770b\":\"身份验证\",\"Ac0clynFHW\":\"完成注册\",\"Aqpi9uEJOQ\":\"暂不支持在 Webview 内设置安全密钥\",\"AxgoV7E0Dt\":\"请输入关联的手机号\",\"BB0h7uM3tf\":\"使用手机接收短信验证码，进行验证\",\"BMdqALcjIc\":\"短信登录\",\"BV4Pu8YMOq\":\"我已阅读并同意\",\"BhHJ6IGasL\":\"密码长度大于等于8；\",\"CZA7vtcDTG\":\"邮箱验证身份\",\"CsaJeMjjZh\":\"激活链接已过期，请联系管理员重新发送\",\"EUTuIvZZ42\":\"至少包含一个大写字母；\",\"FFLJg3RNHj\":\"手机号以及\",\"GX6AxEh26g\":\"邮箱验证\",\"GZ9CdDVptD\":\"选择国家或地区\",\"Gky2qDRQxU\":\"请重新输入密码\",\"Gm5RvhFMuI\":\"至少包含一个特殊字符；\",\"H4SdoGFoAV\":\"{value}位连续相同字符\",\"HDhvm8vw43\":\"{second}s后可重新获取\",\"HwReGyoEEH\":\"邮件发送成功\",\"JIhWmbtUZO\":\"数字\",\"JxQY9rS6v6\":\"邮箱登录\",\"La5xme7PUb\":\"返回\",\"LubyLQCUZt\":\"设置密码\",\"NEbSkfETAO\":\"大写字母\",\"ONFuOLKfOq\":\"使用协议\",\"Of9bNXFbtk\":\"请联系管理员\",\"Olh7HmG6rd\":\"没有收到邮箱?\",\"PQSc8RF7Sv\":\"请输入手机号码\",\"PvInpNJ6eY\":\"两次输入的密码不一致\",\"QalvzHENe8\":\"激活您的账号，请先设置新密码\",\"Qge3J7Lqej\":\"使用 OTP 应用生成一次性验证码\",\"R9VHqP4e1i\":\"手机验证\",\"RCKKWYP6i0\":\"请填写您的邮箱，重置密码前我们需要验证您的身份\",\"RidjB1MuVt\":\"账号登录\",\"RwQPax9zvU\":\"邮箱验证\",\"S7E11c5xwr\":\"请输入6位短信验证码\",\"STAaaucD4i\":\"根据公司安全合规要求:\",\"SgXXkOO0EM\":\"账号激活成功\",\"Tgz1o8mIrH\":\"如果还没收到邮件，请选择\",\"Tq7wfcFEZc\":\"小写字母\",\"Ur8Pl3DuKv\":\"获取验证码\",\"VCmJvXUq1f\":\"二次验证\",\"VHijkZvWsz\":\"请输入6位邮箱验证码\",\"VemImsGGrK\":\"特殊字符\",\"VmdECOmvVz\":\"手机号有误\",\"W75OVBIcUY\":\"请输入邮箱验证码\",\"WOtoNkuXKd\":\"使用邮箱接收验证码，进行验证\",\"YEHeCrVQhO\":\"手机验证\",\"YIpg6N3L5d\":\"重置密码\",\"YsgwAQzRWo\":\"请输入新密码\",\"Ywt20xudDr\":\"下一步\",\"ZXA32CTb6B\":\"FIDO2 设备识别认证\",\"ZbLwOTA6PD\":\"请输入\",\"aKnzlVREVN\":\"邮箱验证\",\"augcWVLjdK\":\"隐私政策\",\"b39909964fbb66de3748954806676cdf\":\"请输入邮箱账号\",\"bFKljHXOd0\":\"请输入邮箱前缀或邮箱地址\",\"c5c3583bfcc51de4ee921b5679a092cc\":\"发送验证码\",\"cLFcVK7UNE\":\"使用 FIDO2 设备进行验证\",\"cU0GWpz7Fz\":\"请输入邮箱\",\"checking\":\"正在校验\",\"collapse\":\"收起\",\"dGjeZgks04\":\"接收短信验证码进行验证\",\"e39ffe99e93cb6cc8d5978ed21660740\":\"请输入密码\",\"eVU5tvjmxP\":\"OTP 验证\",\"expand\":\"展开\",\"f5MtKaUWxO\":\"用户注册\",\"fQ84MVTQQN\":\"不能与最近{value}次历史密码相同\",\"fiqGCtxwTg\":\"请选择二次认证方式\",\"fk0WuNDexe\":\"其他登录方式\",\"g9DljZ5sir\":\"我们已向你的邮箱发送了密码重置链接, 请注意查收\",\"gNBX2opsPu\":\"不允许包含\",\"hNhYSSnFpH\":\"请输入一次性验证码\",\"hTfgWRNnGP\":\"请输入你希望使用的个人密码\",\"hYnIMZY94Y\":\"选择其他认证方式\",\"iXSYQC3TAk\":\"账号信息\",\"ivY5H7GDGT\":\"激活账号\",\"ivmlfJKutz\":\"选择注册方式\",\"jzeyjoJc47\":\"忘记密码\",\"kLD1GUcqXK\":\"请输入用户名\",\"l3uoNq1Vn7\":\"确认\",\"lkiEvOvijA\":\"公司相关术语(例: zijie, toutiao 等)\",\"lmKD2QzOCZ\":\"{value}位以上， 需包含\",\"loading\":\"正在加载...\",\"m6cv4vD7TZ\":\"相关承诺\",\"mfa_not_yet\":\"当前认证因子不可用, 请选择其他因子进行认证\",\"oJHJl8gtcZ\":\"请输入密码\",\"ougBepXqDz\":\"请检查垃圾邮件箱\",\"p24lcioxlQ\":\"缺少token信息\",\"pdd4S0Dmw5\":\"登录\",\"please_wait\":\"请稍候\",\"qGAPRfspJ2\":\"其他方式登录\",\"rVeqSeGM9X\":\"包含数字和字母；\",\"reZoEptA2K\":\"简单密码（例：123456，abcdef）\",\"setup_token_expired\":\"激活链接已过期，请联系管理员重新发送\",\"spFbnKg9rR\":\"使用邮箱接收验证码进行验证\",\"tf6UKvBtzX\":\"返回登录页面\",\"tiSTyKybnN\":\"返回 SSO 登录\",\"u4aavRFPBp\":\"或其他违禁词汇\",\"uYmgRxdqke\":\"暂不支持注册\",\"ukg8YPRfE5\":\"密码设置成功\",\"vKfccBL22P\":\"请输入6位短信验证码\",\"vjlW6ZOvZ4\":\"切换为邮箱验证\",\"w6I2wlD3xP\":\"重新发送邮件\",\"xV5SawPyBC\":\"输入密码\",\"xrBJmjmG9t\":\"找回密码\",\"yFi4u6488q\":\"邮件验证码错误或过期，请重新输入\",\"yj1BoKEIQh\":\"切换为手机验证\",\"ylEZOpFnXZ\":\"请填写正确邮箱\",\"idp_sso\":\"登录\",\"idp_lark\":\"飞书\",\"idp_google\":\"谷歌\",\"idp_passkey\":\"通行密钥\",\"idp_passkey_confirm\":\"检测到当前没有设置过 Passkey, 是否在登录完成后进行 Passkey 设置, 设置完成后会自动跳转至应用\",\"d7098f5050f017673319c5db1473ada7\":\"打开\",\"c5a0252316e8567d0c7d7f458c4fa4c1\":\"扫描二维码\",\"153ff41538ac4d1b966e9e964642d385\":\"二维码已失效\",\"cc860ffa3a23ca375ea9320efc8f11d0\":\"刷新二维码\",\"FnMTwUHFMH\":\"验证中\",\"ArCd2APVvE\":\"验证成功, 正在跳转\",\"W2jXqew6gd\":\"操作超时或用户取消\",\"HBcPpbxZS75i8XHNiKcqVaoQJvKn6W6q\":\"如果忘记密码，请尝试通过 \",\"wPFna14mecb43Rf7cRhtO3Rs6XiHNMH4\":\"解锁账号\",\"ycab9lD63s\":\"用户名\",\"dwdEHfFk3M\":\"邮箱\",\"VQwsW02Zdw\":\"或其中连续字符\",\"d98a14a912486c6be40db3e768568e73\":\"请再次输入新密码\",\"53d7c6aa7faa9c27555919ae43044684\":\"两次密码输入不一致\",\"Please get verification code\":\"请点击发送验证码\",\"fido2_passkey_desc\":\"使用通行密钥进行验证\",\"fido2_securitykey_desc\":\"使用安全密钥进行验证\",\"fido2_passkey_label\":\"通行密钥认证\",\"fido2_securitykey_label\":\"安全密钥认证\",\"BZRB0y9icF\":\"系统检测到您当前使用的密码不符合密码安全策略，请设置新密码并在 1 分钟后使用新密码登录。\",\"to_setting\":\"去设置\",\"set_unenabled_mfa_desc\":\"使用当前已开启的因子进行认证, 认证成功后进行设置\",\"unavailable\":\"暂不可用\",\"authn_description\":\"由于涉及敏感数据，为了保障信息安全，需要您先完成身份认证。\",\"extra-verification\":\"我无法使用上述任意一种方式进行验证\",\"enter_password\":\"输入密码\",\"enter_account\":\"输入账号\",\"redirect_loading\":\"正在跳转\",\"set_passkey\":\"设置通行密钥\",\"add_passkey\":\"添加通行密钥\",\"add_later\":\"稍后添加\",\"fido_tip_title\":\"一步认证提示\",\"idp_passkey_tip\":\"如果你还未开启通行密钥，可以在登录成功后访问\\\"身份验证器\\\"页面进行设置。\",\"passkey_advance\":\"开启通行密钥后，你可以使用指纹、面部或屏幕锁来验证你的身份。\",\"reference\":\"FIDO 一步认证 Q&A\",\"use_passkey\":\"使用通行密钥认证\",\"use_password\":\"使用密码认证\",\"authenticating\":\"认证中\",\"recommend\":\"推荐\",\"authentication_selection\":\"选择认证方式\",\"recommend_passkey_description\":\"检测到当前账号已开启通行密钥，推荐使用通行密钥进行认证\",\"plugin_recommend_passkey_description\":\"登录成功后优先开启通行密钥\",\"passkey_entry\":\"改用通行密钥认证\",\"forgot_interceptor\":\"根据安全和合规要求, 请在内网环境下修改密码\",\"forgot_interceptor_vpn\":\"请连接公司Wi-Fi或尝试连接VPN\",\"passkey_login_in_iframe\":\"检测到当前处于 iframe 环境, 需要应用在 iframe 标签添加 `allow=\\\"publickey-credentials-get *\\\"` 属性来允许使用通行密钥\",\"unknown_passkey_domain\":\"检测到当前域名为容灾域名, 请先切换到线上或常用域名\",\"passkey_not_more\":\"不再提示\",\"set_successfully\":\"设置成功\",\"logo\":\"字节跳动统一登录平台\",\"df9y6J9aO8\":\"{year}字节跳动统一登录平台\",\"iframeNotice\":\"检测到当前为 Iframe 环境，由于三方 Cookie 限制，需要重新登录 ByteDance SSO\",\"notice_link_constant\":\"SSO 现已支持通行密钥一键安全登录！告别繁琐密码和 MFA，使用生物识别/设备PIN快速验证，快按照文档试试吧: \",\"passkey_recommand_btn\":\"SSO开启通行密钥用户手册\",\"passkey_recommand_link\":\"https://bytedance.us.larkoffice.com/docx/doxus1wsemosGqroI05VOI1p0kf\",\"sso_fallback\":\"为了应对公司 0424 容灾演练，SSO 预计在 4月24日 00:00-08:00 进行变更验证，理论上无影响，如有影响，请联系 SSO Oncall 处理\"},\"IDP_CACHE\":\"lark\",\"username\":\"<EMAIL>\",\"suffix\":\"\"}"}, {"name": "__tea_cache_first_2227", "value": "1"}, {"name": "__tea_cache_tokens_2227", "value": "{\"web_id\":\"7519078114262058533\",\"user_unique_id\":\"7519078114262058533\",\"timestamp\":1750671807006,\"_type_\":\"default\"}"}, {"name": "SLARDARsso_fe_web", "value": "JTdCJTIydXNlcklkJTIyOiUyMjkyZmJhNzNiLTc5MTQtNGI1NC04NGJlLTc5NGQ2MDQ4OTUyOSUyMiwlMjJkZXZpY2VJZCUyMjolMjJkN2IzNDI4Zi01ODk4LTQ3OGEtOTg4Mi05NTQ5YWU0YTQ3OTQlMjIsJTIyZXhwaXJlcyUyMjoxNzU4NDQ3ODA3MjU2JTdE"}]}]}