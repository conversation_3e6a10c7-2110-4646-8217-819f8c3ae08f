# Python
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
env/
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
*.egg-info/
.installed.cfg
*.egg

# Flask
instance/
.webassets-cache

# 虚拟环境
venv/
ENV/
env/
.venv/

# 数据库
*.sqlite
*.db

# 日志文件
*.log
logs/


# IDE相关
.idea/
.vscode/
*.swp
*.swo
.DS_Store

# 环境变量
! lightrag/.env
.flaskenv

# 临时文件
tmp/
temp/

# 上传的文件
uploads/

# 生成的报告
reports/

# 配置文件（可能包含敏感信息）
config.local.py

# 缓存
__pycache__/
.pytest_cache/
.coverage
htmlcov/


*__pycache__/

# 依赖管理
pip-log.txt
pip-delete-this-directory.txt