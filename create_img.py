import matplotlib.pyplot as plt
import numpy as np
import os
from matplotlib.patches import FancyBboxPatch

# 确保输出目录存在
os.makedirs('output', exist_ok=True)

# 设置中文字体
plt.rcParams['font.family'] = 'sans-serif'
plt.rcParams['font.sans-serif'] = ['Noto Sans CJK JP'] + plt.rcParams['font.sans-serif']


# 5. 数字人分析师工作流程图
def create_digital_analyst_workflow():
    fig, ax = plt.subplots(figsize=(12, 8))
    
    # 背景设置
    ax.set_xlim(0, 12)
    ax.set_ylim(0, 8)
    ax.axis('off')
    
    # 用户提问
    user_box = FancyBboxPatch((0.5, 6.5), 3, 1, fc='#D6EAF8', ec='black', alpha=0.7, 
                              boxstyle='round,pad=0.3')
    ax.add_patch(user_box)
    ax.text(2, 7, '用户提问', ha='center', fontsize=12, fontweight='bold')
    ax.text(2, 6.7, '例："上个季度非一线占比如何？"', ha='center', fontsize=10)
    
    # Transformer理解
    transformer_box = FancyBboxPatch((4.5, 6.5), 3, 1, fc='#D5F5E3', ec='black', alpha=0.7, 
                                    boxstyle='round,pad=0.3')
    ax.add_patch(transformer_box)
    ax.text(6, 7, 'Transformer理解问题', ha='center', fontsize=12, fontweight='bold')
    ax.text(6, 6.7, '理解意图和关键要素', ha='center', fontsize=10)
    
    # 数据检索
    retrieval_box = FancyBboxPatch((8.5, 6.5), 3, 1, fc='#FADBD8', ec='black', alpha=0.7, 
                                  boxstyle='round,pad=0.3')
    ax.add_patch(retrieval_box)
    ax.text(10, 7, '数据检索', ha='center', fontsize=12, fontweight='bold')
    ax.text(10, 6.7, '查询相关数据源', ha='center', fontsize=10)
    
    # 数据分析
    analysis_box = FancyBboxPatch((8.5, 4.5), 3, 1, fc='#FCF3CF', ec='black', alpha=0.7, 
                                  boxstyle='round,pad=0.3')
    ax.add_patch(analysis_box)
    ax.text(10, 5, '数据分析', ha='center', fontsize=12, fontweight='bold')
    ax.text(10, 4.7, '处理和分析数据', ha='center', fontsize=10)
    
    # 生成回答
    answer_box = FancyBboxPatch((4.5, 4.5), 3, 1, fc='#E8DAEF', ec='black', alpha=0.7, 
                                boxstyle='round,pad=0.3')
    ax.add_patch(answer_box)
    ax.text(6, 5, 'Transformer生成回答', ha='center', fontsize=12, fontweight='bold')
    ax.text(6, 4.7, '整合分析结果', ha='center', fontsize=10)
    
    # 用户获得回答
    result_box = FancyBboxPatch((0.5, 4.5), 3, 1, fc='#D6EAF8', ec='black', alpha=0.7, 
                                boxstyle='round,pad=0.3')
    ax.add_patch(result_box)
    ax.text(2, 5, '用户获得回答', ha='center', fontsize=12, fontweight='bold')
    ax.text(2, 4.7, '包含数据可视化和解释', ha='center', fontsize=10)
    
    # 连接箭头
    plt.arrow(3.5, 7, 0.8, 0, head_width=0.2, head_length=0.2, fc='black', ec='black')
    plt.arrow(7.5, 7, 0.8, 0, head_width=0.2, head_length=0.2, fc='black', ec='black')
    plt.arrow(10, 6.3, 0, -0.6, head_width=0.2, head_length=0.2, fc='black', ec='black')
    plt.arrow(8.3, 5, -0.6, 0, head_width=0.2, head_length=0.2, fc='black', ec='black')
    plt.arrow(4.3, 5, -0.6, 0, head_width=0.2, head_length=0.2, fc='black', ec='black')
    
    # RAG知识增强
    rag_box = FancyBboxPatch((4.5, 2.5), 7, 1, fc='#ABEBC6', ec='black', alpha=0.7, 
                             boxstyle='round,pad=0.3')
    ax.add_patch(rag_box)
    ax.text(8, 3, 'RAG知识空间增强', ha='center', fontsize=12, fontweight='bold')
    ax.text(8, 2.7, '提供专业知识、最新数据和行业背景', ha='center', fontsize=10)
    
    # RAG连接
    plt.arrow(6, 4.3, 0, -0.6, head_width=0.2, head_length=0.2, fc='black', ec='black', linestyle='--')
    plt.arrow(10, 4.3, 0, -0.6, head_width=0.2, head_length=0.2, fc='black', ec='black', linestyle='--')
    
    # 标题
    ax.text(6, 8, 'c宝数字人分析师工作流程', ha='center', fontsize=16, fontweight='bold')
    
    # 说明
    ax.text(6, 1.5, 'Transformer技术为整个流程提供了强大的语言理解和生成能力\nRAG知识空间提供了专业领域知识支持，使分析更加准确和专业', 
            ha='center', fontsize=12, style='italic')
    
    plt.tight_layout()
    plt.savefig('output/digital_analyst_workflow.png', dpi=300, bbox_inches='tight')
    plt.close()

# 6. RAG工作原理图
def create_rag_mechanism():
    fig, ax = plt.subplots(figsize=(12, 8))
    
    # 背景设置
    ax.set_xlim(0, 12)
    ax.set_ylim(0, 8)
    ax.axis('off')
    
    # 用户查询
    query_box = plt.Rectangle((0.5, 6), 2, 1, fc='#D6EAF8', ec='black', alpha=0.7)
    ax.add_patch(query_box)
    ax.text(1.5, 6.5, '用户查询', ha='center', fontsize=12)
    
    # Transformer编码
    encode_box = plt.Rectangle((3.5, 6), 2, 1, fc='#D5F5E3', ec='black', alpha=0.7)
    ax.add_patch(encode_box)
    ax.text(4.5, 6.5, '查询编码', ha='center', fontsize=12)
    
    # 知识库
    kb_box = plt.Rectangle((3.5, 3), 2, 2, fc='#FADBD8', ec='black', alpha=0.7)
    ax.add_patch(kb_box)
    ax.text(4.5, 4, '知识库', ha='center', fontsize=12)
    ax.text(4.5, 3.5, '文档、数据\n行业知识', ha='center', fontsize=10)
    
    # 检索
    retrieve_box = plt.Rectangle((6.5, 4.5), 2, 1, fc='#FCF3CF', ec='black', alpha=0.7)
    ax.add_patch(retrieve_box)
    ax.text(7.5, 5, '相关内容检索', ha='center', fontsize=12)
    
    # 增强上下文
    context_box = plt.Rectangle((9.5, 6), 2, 1, fc='#E8DAEF', ec='black', alpha=0.7)
    ax.add_patch(context_box)
    ax.text(10.5, 6.5, '增强上下文', ha='center', fontsize=12)
    
    # Transformer生成
    generate_box = plt.Rectangle((6.5, 6), 2, 1, fc='#ABEBC6', ec='black', alpha=0.7)
    ax.add_patch(generate_box)
    ax.text(7.5, 6.5, 'Transformer\n处理', ha='center', fontsize=12)
    
    # 回答
    answer_box = plt.Rectangle((9.5, 3), 2, 1, fc='#D6EAF8', ec='black', alpha=0.7)
    ax.add_patch(answer_box)
    ax.text(10.5, 3.5, '生成回答', ha='center', fontsize=12)
    
    # 连接箭头
    plt.arrow(2.5, 6.5, 0.8, 0, head_width=0.2, head_length=0.2, fc='black', ec='black')
    plt.arrow(5.5, 6.5, 0.8, 0, head_width=0.2, head_length=0.2, fc='black', ec='black')
    plt.arrow(8.5, 6.5, 0.8, 0, head_width=0.2, head_length=0.2, fc='black', ec='black')
    plt.arrow(4.5, 5.8, 0, -0.6, head_width=0.2, head_length=0.2, fc='black', ec='black')
    plt.arrow(5.7, 4, 0.6, 0.3, head_width=0.2, head_length=0.2, fc='black', ec='black')
    plt.arrow(7.5, 5.8, 0, -0.6, head_width=0.2, head_length=0.2, fc='black', ec='black')
    plt.arrow(8.5, 5, 0.8, -1.3, head_width=0.2, head_length=0.2, fc='black', ec='black')
    plt.arrow(10.5, 5.8, 0, -1.6, head_width=0.2, head_length=0.2, fc='black', ec='black')
    
    # 标题
    ax.text(6, 7.5, 'RAG (检索增强生成) 工作原理', ha='center', fontsize=16, fontweight='bold')
    
    # 说明
    ax.text(6, 1.5, 'RAG结合了检索系统和生成模型的优势：\n检索系统提供最相关的知识，Transformer模型负责理解和生成连贯的回答', 
            ha='center', fontsize=12, style='italic')
    
    plt.tight_layout()
    plt.savefig('output/rag_mechanism.png', dpi=300, bbox_inches='tight')
    plt.close()

# 执行所有图表生成函数
create_digital_analyst_workflow()
#create_rag_mechanism()

print("所有可视化图表已成功生成并保存到output目录")
